#include "Application.h"
#include "geometry/GeometryEngine.h"
#include "opengl/Renderer.h"
#include "api/Server.h"
#include <iostream>
#include <chrono>
#include <thread>

// Global application instance
std::unique_ptr<Application> g_app;

Application::Application() : running_(false) {
    std::cout << "🎯 Creating Application instance" << std::endl;
}

Application::~Application() {
    std::cout << "🔄 Destroying Application instance" << std::endl;
    Shutdown();
}

bool Application::Initialize() {
    std::cout << "🔧 Initializing Application components..." << std::endl;
    
    try {
        // Initialize geometry engine
        if (!InitializeGeometry()) {
            std::cerr << "❌ Failed to initialize geometry engine" << std::endl;
            return false;
        }
        
        // Initialize renderer
        if (!InitializeRenderer()) {
            std::cerr << "❌ Failed to initialize renderer" << std::endl;
            return false;
        }
        
        // Initialize server
        if (!InitializeServer()) {
            std::cerr << "❌ Failed to initialize server" << std::endl;
            return false;
        }
        
        running_ = true;
        std::cout << "✅ All components initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Exception during initialization: " << e.what() << std::endl;
        return false;
    }
}

void Application::Run() {
    if (!running_) {
        std::cerr << "❌ Application not initialized" << std::endl;
        return;
    }
    
    std::cout << "🚀 Starting application main loop" << std::endl;
    
    // Start server in separate thread
    serverThread_ = std::thread(&Application::ServerLoop, this);
    
    // Run main render loop
    RenderLoop();
    
    // Wait for server thread to finish
    if (serverThread_.joinable()) {
        serverThread_.join();
    }
}

void Application::Shutdown() {
    if (!running_) {
        return;
    }
    
    std::cout << "🔄 Shutting down application..." << std::endl;
    running_ = false;
    
    // Stop server
    if (server_) {
        server_->Stop();
    }
    
    // Wait for server thread
    if (serverThread_.joinable()) {
        serverThread_.join();
    }
    
    // Shutdown renderer
    if (renderer_) {
        renderer_->Shutdown();
    }
    
    std::cout << "✅ Application shutdown complete" << std::endl;
}

bool Application::InitializeGeometry() {
    std::cout << "🔧 Initializing Geometry Engine..." << std::endl;
    
    try {
        geometryEngine_ = std::make_unique<GeometryEngine>();
        std::cout << "✅ Geometry Engine initialized" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "❌ Geometry Engine initialization failed: " << e.what() << std::endl;
        return false;
    }
}

bool Application::InitializeRenderer() {
    std::cout << "🔧 Initializing OpenGL Renderer..." << std::endl;
    
    try {
        renderer_ = std::make_unique<Renderer>();
        
        bool success = renderer_->Initialize(
            config_.windowWidth, 
            config_.windowHeight, 
            config_.headless
        );
        
        if (success) {
            std::cout << "✅ OpenGL Renderer initialized" << std::endl;
            return true;
        } else {
            std::cerr << "❌ OpenGL Renderer initialization failed" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "❌ Renderer initialization failed: " << e.what() << std::endl;
        return false;
    }
}

bool Application::InitializeServer() {
    std::cout << "🔧 Initializing Web Server..." << std::endl;
    
    try {
        server_ = std::make_unique<Server>(config_.serverPort);
        
        // Set backend components
        server_->SetGeometryEngine(geometryEngine_);
        server_->SetRenderer(renderer_);
        
        // Enable CORS for web frontend
        server_->EnableCORS(true);
        server_->SetCORSOrigin("*");
        
        std::cout << "✅ Web Server initialized" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "❌ Server initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void Application::RenderLoop() {
    std::cout << "🖥️  Starting render loop..." << std::endl;
    
    auto lastTime = std::chrono::high_resolution_clock::now();
    int frameCount = 0;
    
    while (running_ && !renderer_->ShouldClose()) {
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
        lastTime = currentTime;
        
        // Begin frame
        renderer_->BeginFrame();
        renderer_->Clear();
        
        // Render current scene
        // (Scene rendering will be implemented based on current geometry)
        
        // End frame
        renderer_->EndFrame();
        renderer_->SwapBuffers();
        renderer_->PollEvents();
        
        // Update frame statistics
        frameCount++;
        if (frameCount % 60 == 0) {
            const auto& stats = renderer_->GetStats();
            if (config_.logLevel == "DEBUG") {
                std::cout << "🎮 FPS: " << stats.fps 
                         << ", Frame time: " << stats.frameTime << "ms" << std::endl;
            }
        }
        
        // Small delay to prevent 100% CPU usage
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    std::cout << "🖥️  Render loop finished" << std::endl;
}

void Application::ServerLoop() {
    std::cout << "🌐 Starting server loop..." << std::endl;
    
    if (!server_->Start()) {
        std::cerr << "❌ Failed to start server" << std::endl;
        running_ = false;
        return;
    }
    
    std::cout << "✅ Server started successfully on port " << config_.serverPort << std::endl;
    
    // Server runs in its own thread, just wait for shutdown
    while (running_ && server_->IsRunning()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "🌐 Server loop finished" << std::endl;
}
