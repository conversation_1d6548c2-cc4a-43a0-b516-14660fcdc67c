<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Container Generator - Bambu Studio Style</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="src/css/reset.css">
    <link rel="stylesheet" href="src/css/variables.css">
    <link rel="stylesheet" href="src/css/layout.css">
    <link rel="stylesheet" href="src/css/components.css">
    <link rel="stylesheet" href="src/css/bambu-theme.css">
    
    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/controls/OrbitControls.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/loaders/STLLoader.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/exporters/STLExporter.js"></script>
</head>
<body>
    <!-- Main Application Container -->
    <div id="app" class="app-container">
        
        <!-- Top Toolbar -->
        <header class="toolbar">
            <div class="toolbar-section toolbar-left">
                <div class="logo">
                    <i class="fas fa-cube"></i>
                    <span>Container Generator</span>
                </div>
                <div class="toolbar-divider"></div>
                <button class="toolbar-btn" id="newBtn" title="New Project">
                    <i class="fas fa-file"></i>
                    <span>New</span>
                </button>
                <button class="toolbar-btn" id="openBtn" title="Open Project">
                    <i class="fas fa-folder-open"></i>
                    <span>Open</span>
                </button>
                <button class="toolbar-btn" id="saveBtn" title="Save Project">
                    <i class="fas fa-save"></i>
                    <span>Save</span>
                </button>
                <div class="toolbar-divider"></div>
                <button class="toolbar-btn" id="exportBtn" title="Export STL">
                    <i class="fas fa-download"></i>
                    <span>Export STL</span>
                </button>
            </div>
            
            <div class="toolbar-section toolbar-center">
                <div class="view-controls">
                    <button class="view-btn active" data-view="solid" title="Solid View">
                        <i class="fas fa-cube"></i>
                    </button>
                    <button class="view-btn" data-view="wireframe" title="Wireframe View">
                        <i class="fas fa-project-diagram"></i>
                    </button>
                    <button class="view-btn" data-view="xray" title="X-Ray View">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            
            <div class="toolbar-section toolbar-right">
                <button class="toolbar-btn" id="resetViewBtn" title="Reset View">
                    <i class="fas fa-home"></i>
                </button>
                <button class="toolbar-btn" id="fitViewBtn" title="Fit View">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <div class="toolbar-divider"></div>
                <div class="status-indicator">
                    <span id="statusText">Ready</span>
                    <div class="status-dot" id="statusDot"></div>
                </div>
            </div>
        </header>
        
        <!-- Main Content Area -->
        <main class="main-content">
            
            <!-- Left Panel - Tools -->
            <aside class="left-panel">
                <div class="panel-section">
                    <h3 class="panel-title">
                        <i class="fas fa-shapes"></i>
                        Shape Tools
                    </h3>
                    <div class="shape-tools">
                        <button class="shape-btn active" data-shape="box">
                            <i class="fas fa-cube"></i>
                            <span>Box</span>
                        </button>
                        <button class="shape-btn" data-shape="cylinder">
                            <i class="fas fa-circle"></i>
                            <span>Cylinder</span>
                        </button>
                        <button class="shape-btn" data-shape="prism">
                            <i class="fas fa-gem"></i>
                            <span>Prism</span>
                        </button>
                    </div>
                </div>
                
                <div class="panel-section">
                    <h3 class="panel-title">
                        <i class="fas fa-edit"></i>
                        Edge Tools
                    </h3>
                    <div class="edge-tools">
                        <button class="tool-btn" id="roundEdgesBtn">
                            <i class="fas fa-circle"></i>
                            <span>Round Edges</span>
                        </button>
                        <button class="tool-btn" id="chamferEdgesBtn">
                            <i class="fas fa-cut"></i>
                            <span>Chamfer Edges</span>
                        </button>
                        <button class="tool-btn" id="resetEdgesBtn">
                            <i class="fas fa-undo"></i>
                            <span>Reset Edges</span>
                        </button>
                    </div>
                </div>
                
                <div class="panel-section">
                    <h3 class="panel-title">
                        <i class="fas fa-info-circle"></i>
                        Model Info
                    </h3>
                    <div class="model-info">
                        <div class="info-item">
                            <span class="info-label">Vertices:</span>
                            <span class="info-value" id="vertexCount">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Faces:</span>
                            <span class="info-value" id="faceCount">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Volume:</span>
                            <span class="info-value" id="volumeValue">0 cm³</span>
                        </div>
                    </div>
                </div>
            </aside>
            
            <!-- Center Panel - 3D Viewport -->
            <section class="center-panel">
                <div class="viewport-header">
                    <h3 class="viewport-title">3D Viewport</h3>
                    <div class="viewport-controls">
                        <button class="viewport-btn" id="gridToggle" title="Toggle Grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="viewport-btn" id="axesToggle" title="Toggle Axes">
                            <i class="fas fa-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="viewport-container">
                    <canvas id="viewport3d" class="viewport-canvas"></canvas>
                    <div class="viewport-overlay">
                        <div class="loading-spinner" id="loadingSpinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Generating...</span>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Right Panel - Properties -->
            <aside class="right-panel">
                <div class="panel-tabs">
                    <button class="tab-btn active" data-tab="geometry">
                        <i class="fas fa-ruler-combined"></i>
                        <span>Geometry</span>
                    </button>
                    <button class="tab-btn" data-tab="material">
                        <i class="fas fa-palette"></i>
                        <span>Material</span>
                    </button>
                    <button class="tab-btn" data-tab="export">
                        <i class="fas fa-download"></i>
                        <span>Export</span>
                    </button>
                </div>
                
                <!-- Geometry Tab -->
                <div class="tab-content active" id="geometryTab">
                    <div class="property-group">
                        <h4 class="group-title">Dimensions (mm)</h4>
                        <div class="property-row">
                            <label for="lengthInput">Length/Diameter:</label>
                            <input type="number" id="lengthInput" value="100" min="1" max="1000" step="0.1">
                        </div>
                        <div class="property-row">
                            <label for="widthInput">Width:</label>
                            <input type="number" id="widthInput" value="60" min="1" max="1000" step="0.1">
                        </div>
                        <div class="property-row">
                            <label for="heightInput">Height:</label>
                            <input type="number" id="heightInput" value="40" min="1" max="1000" step="0.1">
                        </div>
                        <div class="property-row">
                            <label for="thicknessInput">Wall Thickness:</label>
                            <input type="number" id="thicknessInput" value="3" min="0.1" max="50" step="0.1">
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <h4 class="group-title">Edge Modification</h4>
                        <div class="property-row">
                            <label for="edgeTypeSelect">Edge Type:</label>
                            <select id="edgeTypeSelect">
                                <option value="sharp">Sharp</option>
                                <option value="rounded">Rounded</option>
                                <option value="chamfered">Chamfered</option>
                            </select>
                        </div>
                        <div class="property-row">
                            <label for="edgeSizeInput">Radius/Size:</label>
                            <input type="number" id="edgeSizeInput" value="0" min="0" max="20" step="0.1">
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <h4 class="group-title">Quality</h4>
                        <div class="property-row">
                            <label for="qualitySelect">Mesh Quality:</label>
                            <select id="qualitySelect">
                                <option value="low">Low (Fast)</option>
                                <option value="medium">Medium</option>
                                <option value="high" selected>High</option>
                                <option value="ultra">Ultra (Slow)</option>
                            </select>
                        </div>
                        <div class="property-row">
                            <label for="segmentsInput">Segments:</label>
                            <input type="number" id="segmentsInput" value="32" min="6" max="128" step="1">
                        </div>
                    </div>
                </div>
                
                <!-- Material Tab -->
                <div class="tab-content" id="materialTab">
                    <div class="property-group">
                        <h4 class="group-title">Material Properties</h4>
                        <div class="property-row">
                            <label for="materialSelect">Material:</label>
                            <select id="materialSelect">
                                <option value="pla">PLA</option>
                                <option value="petg">PETG</option>
                                <option value="abs">ABS</option>
                                <option value="tpu">TPU</option>
                            </select>
                        </div>
                        <div class="property-row">
                            <label for="colorPicker">Color:</label>
                            <input type="color" id="colorPicker" value="#4A90E2">
                        </div>
                    </div>
                </div>
                
                <!-- Export Tab -->
                <div class="tab-content" id="exportTab">
                    <div class="property-group">
                        <h4 class="group-title">Export Settings</h4>
                        <div class="property-row">
                            <label for="unitsSelect">Units:</label>
                            <select id="unitsSelect">
                                <option value="mm" selected>Millimeters</option>
                                <option value="cm">Centimeters</option>
                                <option value="in">Inches</option>
                            </select>
                        </div>
                        <div class="property-row">
                            <label for="exportQualitySelect">Export Quality:</label>
                            <select id="exportQualitySelect">
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high" selected>High</option>
                                <option value="ultra">Ultra</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <button class="export-btn primary" id="exportSTLBtn">
                            <i class="fas fa-download"></i>
                            Export STL File
                        </button>
                        <button class="export-btn secondary" id="exportOBJBtn">
                            <i class="fas fa-download"></i>
                            Export OBJ File
                        </button>
                    </div>
                </div>
            </aside>
            
        </main>
        
    </div>
    
    <!-- JavaScript Modules -->
    <script type="module" src="src/js/main.js"></script>
</body>
</html>
