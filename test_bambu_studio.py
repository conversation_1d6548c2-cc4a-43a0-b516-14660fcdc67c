#!/usr/bin/env python3
"""
Test script for Bambu Studio Style 3D Container Generator
"""

def test_geometry_engine():
    """Test the geometry engine functionality."""
    print("🧪 Testing Geometry Engine")
    print("=" * 40)
    
    try:
        from geometry_engine import GeometryEngine
        engine = GeometryEngine()
        print("✅ GeometryEngine imported successfully")
        
        # Test box creation
        print("\n1. Testing box creation...")
        box_mesh = engine.create_shape(
            shape_type='box',
            length=100, width=60, height=40,
            wall_thickness=3,
            edge_type='sharp',
            edge_size=0,
            quality='high'
        )
        print(f"   ✅ Box created: {len(box_mesh.vertices)} vertices, {len(box_mesh.faces)} faces")
        
        # Test cylinder creation
        print("\n2. Testing cylinder creation...")
        cylinder_mesh = engine.create_shape(
            shape_type='cylinder',
            length=80, width=None, height=50,
            wall_thickness=3,
            edge_type='sharp',
            edge_size=0,
            quality='high'
        )
        print(f"   ✅ Cylinder created: {len(cylinder_mesh.vertices)} vertices, {len(cylinder_mesh.faces)} faces")
        
        # Test prism creation
        print("\n3. Testing prism creation...")
        prism_mesh = engine.create_shape(
            shape_type='prism',
            length=70, width=6, height=45,  # 6-sided prism
            wall_thickness=3,
            edge_type='sharp',
            edge_size=0,
            quality='high'
        )
        print(f"   ✅ Prism created: {len(prism_mesh.vertices)} vertices, {len(prism_mesh.faces)} faces")
        
        # Test edge rounding
        print("\n4. Testing edge rounding...")
        rounded_box = engine.create_shape(
            shape_type='box',
            length=100, width=60, height=40,
            wall_thickness=3,
            edge_type='rounded',
            edge_size=5,
            quality='medium'
        )
        print(f"   ✅ Rounded box created: {len(rounded_box.vertices)} vertices, {len(rounded_box.faces)} faces")
        
        # Test edge chamfering
        print("\n5. Testing edge chamfering...")
        chamfered_box = engine.create_shape(
            shape_type='box',
            length=100, width=60, height=40,
            wall_thickness=3,
            edge_type='chamfered',
            edge_size=3,
            quality='medium'
        )
        print(f"   ✅ Chamfered box created: {len(chamfered_box.vertices)} vertices, {len(chamfered_box.faces)} faces")
        
        print(f"\n🎉 All geometry tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Geometry test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_opengl_viewer():
    """Test OpenGL viewer functionality."""
    print("\n🖥️  Testing OpenGL Viewer")
    print("=" * 40)
    
    try:
        from opengl_viewer import OpenGLViewer, OPENGL_AVAILABLE
        print(f"✅ OpenGLViewer imported successfully")
        print(f"   OpenGL Available: {OPENGL_AVAILABLE}")
        
        if OPENGL_AVAILABLE:
            print("   ✅ OpenGL support detected")
        else:
            print("   ⚠️  OpenGL not available, will use fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenGL viewer test failed: {e}")
        return False

def test_main_application():
    """Test main application creation."""
    print("\n🚀 Testing Main Application")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from bambu_studio import BambuStudioApp
        
        # Create hidden root window
        root = tk.Tk()
        root.withdraw()
        
        # Create app instance
        app = BambuStudioApp(root)
        print("✅ BambuStudioApp created successfully")
        
        # Test parameter access
        shape = app.shape_var.get()
        length = app.length_var.get()
        print(f"   ✅ Default parameters: shape={shape}, length={length}")
        
        # Cleanup
        root.destroy()
        print("   ✅ App cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Main application test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stl_export():
    """Test STL export functionality."""
    print("\n💾 Testing STL Export")
    print("=" * 40)
    
    try:
        from geometry_engine import GeometryEngine
        import os
        
        engine = GeometryEngine()
        
        # Create a simple box
        mesh = engine.create_shape(
            shape_type='box',
            length=50, width=30, height=20,
            wall_thickness=2,
            edge_type='sharp',
            edge_size=0,
            quality='medium'
        )
        
        # Export to STL
        test_file = "test_export.stl"
        mesh.export(test_file)
        
        if os.path.exists(test_file):
            size = os.path.getsize(test_file)
            print(f"✅ STL export successful: {test_file} ({size} bytes)")
            os.remove(test_file)  # Cleanup
            return True
        else:
            print("❌ STL file not created")
            return False
            
    except Exception as e:
        print(f"❌ STL export test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🎯 Bambu Studio Style 3D Container Generator - Test Suite")
    print("=" * 70)
    
    # Run tests
    geometry_success = test_geometry_engine()
    viewer_success = test_opengl_viewer()
    app_success = test_main_application()
    export_success = test_stl_export()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ Geometry Engine: {'PASS' if geometry_success else 'FAIL'}")
    print(f"✅ OpenGL Viewer: {'PASS' if viewer_success else 'FAIL'}")
    print(f"✅ Main Application: {'PASS' if app_success else 'FAIL'}")
    print(f"✅ STL Export: {'PASS' if export_success else 'FAIL'}")
    
    overall_success = all([geometry_success, viewer_success, app_success, export_success])
    
    print(f"\n🎯 OVERALL: {'✅ ALL TESTS PASSED' if overall_success else '⚠️ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 BAMBU STUDIO STYLE GENERATOR IS READY!")
        print("🚀 Features working:")
        print("   • Professional dark theme UI")
        print("   • OpenGL 3D viewport with fallback")
        print("   • Box, cylinder, and prism generation")
        print("   • Edge rounding and chamfering")
        print("   • STL export functionality")
        print("   • Modern Bambu Lab Studio inspired interface")
        print("\n💡 Launch with: python bambu_studio.py")
    else:
        print("\n⚠️  Some components need attention. Check the test results above.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
