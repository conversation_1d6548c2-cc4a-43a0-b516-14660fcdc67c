#pragma once

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <thread>
#include <atomic>

// Forward declarations
struct Mesh;
struct ContainerParams;
class GeometryEngine;
class Renderer;

/**
 * HTTP request structure
 */
struct HttpRequest {
    std::string method;
    std::string path;
    std::map<std::string, std::string> headers;
    std::string body;
    std::map<std::string, std::string> params;
};

/**
 * HTTP response structure
 */
struct HttpResponse {
    int statusCode = 200;
    std::map<std::string, std::string> headers;
    std::string body;
    
    void SetJSON(const std::string& json);
    void SetFile(const std::string& filename, const std::string& contentType);
    void SetError(int code, const std::string& message);
};

/**
 * Request handler function type
 */
using RequestHandler = std::function<void(const HttpRequest&, HttpResponse&)>;

/**
 * REST API server for the 3D Container Generator
 */
class Server {
public:
    Server(int port = 8081);
    ~Server();

    // Server lifecycle
    bool Start();
    void Stop();
    bool IsRunning() const { return running_; }
    
    // Route registration
    void RegisterRoute(const std::string& method, const std::string& path, RequestHandler handler);
    
    // Set backend components
    void SetGeometryEngine(std::shared_ptr<GeometryEngine> engine) { geometryEngine_ = engine; }
    void SetRenderer(std::shared_ptr<Renderer> renderer) { renderer_ = renderer; }
    
    // CORS settings
    void EnableCORS(bool enable = true) { corsEnabled_ = enable; }
    void SetCORSOrigin(const std::string& origin) { corsOrigin_ = origin; }

private:
    int port_;
    std::atomic<bool> running_;
    std::thread serverThread_;
    
    // Backend components
    std::shared_ptr<GeometryEngine> geometryEngine_;
    std::shared_ptr<Renderer> renderer_;
    
    // Route handling
    std::map<std::string, std::map<std::string, RequestHandler>> routes_;
    
    // CORS settings
    bool corsEnabled_;
    std::string corsOrigin_;
    
    // Server implementation
    void ServerLoop();
    void HandleRequest(const HttpRequest& request, HttpResponse& response);
    void AddCORSHeaders(HttpResponse& response);
    
    // API endpoints
    void RegisterAPIRoutes();
    void HandleGenerateContainer(const HttpRequest& request, HttpResponse& response);
    void HandleGetPreview(const HttpRequest& request, HttpResponse& response);
    void HandleExportSTL(const HttpRequest& request, HttpResponse& response);
    void HandleGetStatus(const HttpRequest& request, HttpResponse& response);
    void HandleOptions(const HttpRequest& request, HttpResponse& response);
    
    // Utility functions
    ContainerParams ParseContainerParams(const std::string& json);
    std::string MeshToJSON(const Mesh& mesh);
    std::string StatusToJSON();
    
    // File serving
    void ServeStaticFile(const std::string& path, HttpResponse& response);
    std::string GetContentType(const std::string& filename);
    
    // Error handling
    void HandleError(HttpResponse& response, int code, const std::string& message);
    
    // Simple HTTP server implementation
    class SimpleHTTPServer {
    public:
        SimpleHTTPServer(int port);
        ~SimpleHTTPServer();
        
        bool Start();
        void Stop();
        void SetRequestHandler(std::function<void(const HttpRequest&, HttpResponse&)> handler);
        
    private:
        int port_;
        int serverSocket_;
        std::atomic<bool> running_;
        std::function<void(const HttpRequest&, HttpResponse&)> requestHandler_;
        
        void AcceptLoop();
        void HandleClient(int clientSocket);
        HttpRequest ParseRequest(const std::string& requestData);
        std::string FormatResponse(const HttpResponse& response);
    };
    
    std::unique_ptr<SimpleHTTPServer> httpServer_;
};
