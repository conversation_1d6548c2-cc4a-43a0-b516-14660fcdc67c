#!/usr/bin/env python3
"""
Launch script for the Enhanced 3D Container Generator.
This script provides an easy way to start the application with proper error handling.
"""

import sys
import os

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = ['trimesh', 'matplotlib', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main launch function."""
    print("🚀 Enhanced 3D Container Generator")
    print("=" * 40)
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies found")
    
    # Check if main files exist
    required_files = ['enhanced_app.py', 'kotak_generator.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Required file missing: {file}")
            sys.exit(1)
    print("✅ All required files found")
    
    # Launch the application
    print("\n🎯 Launching Enhanced 3D Container Generator...")
    print("   - Use the tabbed interface to select container shapes")
    print("   - Adjust parameters in real-time")
    print("   - Preview updates automatically")
    print("   - Export STL files when ready")
    print("\n" + "=" * 40)
    
    try:
        from enhanced_app import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n👋 Application closed by user")
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        print("\n💡 Try running the demo instead:")
        print("   python demo.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
