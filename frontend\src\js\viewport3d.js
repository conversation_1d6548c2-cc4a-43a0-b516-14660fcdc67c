/**
 * Three.js 3D Viewport Component
 * Professional 3D visualization for container preview
 */

export class Viewport3D {
    constructor(canvasId) {
        this.canvasId = canvasId;
        this.canvas = null;
        
        // Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // Scene objects
        this.containerMesh = null;
        this.gridHelper = null;
        this.axesHelper = null;
        this.lights = [];
        
        // Settings
        this.viewMode = 'solid';
        this.showGrid = true;
        this.showAxes = true;
        this.materialColor = 0x4A90E2;
        
        // Animation
        this.animationId = null;
        this.isAnimating = false;
    }
    
    async initialize() {
        console.log('🖥️ Initializing Three.js viewport');
        
        try {
            this.canvas = document.getElementById(this.canvasId);
            if (!this.canvas) {
                throw new Error(`Canvas element '${this.canvasId}' not found`);
            }
            
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupControls();
            this.setupLighting();
            this.setupHelpers();
            
            // Handle window resize
            window.addEventListener('resize', () => this.onWindowResize());
            
            // Start render loop
            this.startAnimation();
            
            console.log('✅ Three.js viewport initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize viewport:', error);
            throw error;
        }
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0f0f0f); // Dark background
        this.scene.fog = new THREE.Fog(0x0f0f0f, 50, 200);
    }
    
    setupCamera() {
        const aspect = this.canvas.clientWidth / this.canvas.clientHeight;
        this.camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
        this.camera.position.set(150, 100, 150);
        this.camera.lookAt(0, 0, 0);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: false
        });
        
        this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
    }
    
    setupControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.canvas);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 10;
        this.controls.maxDistance = 500;
        this.controls.maxPolarAngle = Math.PI / 2;
        
        // Smooth controls
        this.controls.addEventListener('change', () => {
            if (!this.isAnimating) {
                this.render();
            }
        });
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        this.lights.push(ambientLight);
        
        // Main directional light
        const mainLight = new THREE.DirectionalLight(0xffffff, 0.8);
        mainLight.position.set(100, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 500;
        mainLight.shadow.camera.left = -100;
        mainLight.shadow.camera.right = 100;
        mainLight.shadow.camera.top = 100;
        mainLight.shadow.camera.bottom = -100;
        this.scene.add(mainLight);
        this.lights.push(mainLight);
        
        // Fill light
        const fillLight = new THREE.DirectionalLight(0x4A90E2, 0.3);
        fillLight.position.set(-50, 50, -50);
        this.scene.add(fillLight);
        this.lights.push(fillLight);
        
        // Rim light
        const rimLight = new THREE.DirectionalLight(0xffffff, 0.2);
        rimLight.position.set(0, 50, -100);
        this.scene.add(rimLight);
        this.lights.push(rimLight);
    }
    
    setupHelpers() {
        // Grid helper
        this.gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
        this.gridHelper.material.opacity = 0.5;
        this.gridHelper.material.transparent = true;
        if (this.showGrid) {
            this.scene.add(this.gridHelper);
        }
        
        // Axes helper
        this.axesHelper = new THREE.AxesHelper(50);
        this.axesHelper.material.linewidth = 3;
        if (this.showAxes) {
            this.scene.add(this.axesHelper);
        }
    }
    
    async loadMesh(meshData) {
        console.log('📦 Loading mesh into viewport');
        
        try {
            // Remove existing container mesh
            if (this.containerMesh) {
                this.scene.remove(this.containerMesh);
                this.containerMesh.geometry.dispose();
                this.containerMesh.material.dispose();
            }
            
            // Create geometry from mesh data
            const geometry = new THREE.BufferGeometry();
            
            if (meshData.vertices && meshData.faces) {
                // Convert vertices to Float32Array
                const vertices = new Float32Array(meshData.vertices.length * 3);
                for (let i = 0; i < meshData.vertices.length; i++) {
                    const vertex = meshData.vertices[i];
                    vertices[i * 3] = vertex.x || vertex[0] || 0;
                    vertices[i * 3 + 1] = vertex.y || vertex[1] || 0;
                    vertices[i * 3 + 2] = vertex.z || vertex[2] || 0;
                }
                
                // Convert faces to indices
                const indices = [];
                for (let i = 0; i < meshData.faces.length; i++) {
                    const face = meshData.faces[i];
                    if (face.length >= 3) {
                        indices.push(face[0], face[1], face[2]);
                        // Handle quads
                        if (face.length === 4) {
                            indices.push(face[0], face[2], face[3]);
                        }
                    }
                }
                
                geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
                geometry.setIndex(indices);
                geometry.computeVertexNormals();
                
            } else {
                // Fallback: create a simple box
                console.warn('Invalid mesh data, creating fallback box');
                geometry.copy(new THREE.BoxGeometry(100, 40, 60));
            }
            
            // Create material based on view mode
            const material = this.createMaterial();
            
            // Create mesh
            this.containerMesh = new THREE.Mesh(geometry, material);
            this.containerMesh.castShadow = true;
            this.containerMesh.receiveShadow = true;
            
            // Add to scene
            this.scene.add(this.containerMesh);
            
            // Fit view to mesh
            this.fitView();
            
            console.log('✅ Mesh loaded successfully');
            
        } catch (error) {
            console.error('❌ Failed to load mesh:', error);
            
            // Create fallback geometry
            const geometry = new THREE.BoxGeometry(100, 40, 60);
            const material = this.createMaterial();
            this.containerMesh = new THREE.Mesh(geometry, material);
            this.scene.add(this.containerMesh);
        }
    }
    
    createMaterial() {
        const color = new THREE.Color(this.materialColor);
        
        switch (this.viewMode) {
            case 'wireframe':
                return new THREE.MeshBasicMaterial({
                    color: color,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                
            case 'xray':
                return new THREE.MeshPhongMaterial({
                    color: color,
                    transparent: true,
                    opacity: 0.3,
                    side: THREE.DoubleSide
                });
                
            default: // solid
                return new THREE.MeshPhongMaterial({
                    color: color,
                    shininess: 100,
                    specular: 0x222222
                });
        }
    }
    
    setViewMode(mode) {
        this.viewMode = mode;
        
        if (this.containerMesh) {
            this.containerMesh.material.dispose();
            this.containerMesh.material = this.createMaterial();
        }
        
        this.render();
    }
    
    setMaterialColor(color) {
        if (typeof color === 'string') {
            this.materialColor = new THREE.Color(color).getHex();
        } else {
            this.materialColor = color;
        }
        
        if (this.containerMesh) {
            this.containerMesh.material.color.setHex(this.materialColor);
        }
        
        this.render();
    }
    
    toggleGrid() {
        this.showGrid = !this.showGrid;
        
        if (this.showGrid) {
            this.scene.add(this.gridHelper);
        } else {
            this.scene.remove(this.gridHelper);
        }
        
        this.render();
    }
    
    toggleAxes() {
        this.showAxes = !this.showAxes;
        
        if (this.showAxes) {
            this.scene.add(this.axesHelper);
        } else {
            this.scene.remove(this.axesHelper);
        }
        
        this.render();
    }
    
    resetView() {
        this.camera.position.set(150, 100, 150);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
        this.render();
    }
    
    fitView() {
        if (!this.containerMesh) return;
        
        const box = new THREE.Box3().setFromObject(this.containerMesh);
        const size = box.getSize(new THREE.Vector3());
        const center = box.getCenter(new THREE.Vector3());
        
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = this.camera.fov * (Math.PI / 180);
        const distance = maxDim / (2 * Math.tan(fov / 2)) * 1.5;
        
        this.camera.position.copy(center);
        this.camera.position.x += distance;
        this.camera.position.y += distance * 0.5;
        this.camera.position.z += distance;
        
        this.camera.lookAt(center);
        this.controls.target.copy(center);
        this.controls.update();
        
        this.render();
    }
    
    startAnimation() {
        this.isAnimating = true;
        this.animate();
    }
    
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
    
    animate() {
        if (!this.isAnimating) return;
        
        this.animationId = requestAnimationFrame(() => this.animate());
        
        this.controls.update();
        this.render();
    }
    
    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    onWindowResize() {
        if (!this.canvas || !this.camera || !this.renderer) return;
        
        const width = this.canvas.clientWidth;
        const height = this.canvas.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
        this.render();
    }
    
    dispose() {
        this.stopAnimation();
        
        if (this.containerMesh) {
            this.containerMesh.geometry.dispose();
            this.containerMesh.material.dispose();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        window.removeEventListener('resize', this.onWindowResize);
    }
}
