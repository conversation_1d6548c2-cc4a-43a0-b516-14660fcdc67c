/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input,
select,
textarea {
  font: inherit;
  color: inherit;
  background: none;
  border: none;
  outline: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Remove default details/summary styles */
details,
summary {
  display: block;
}

summary {
  cursor: pointer;
}

/* Remove default hr styles */
hr {
  border: none;
  height: 1px;
  background-color: var(--border-primary);
  margin: var(--spacing-md) 0;
}

/* Accessibility */
[hidden] {
  display: none !important;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background-color: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--bg-hover);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--bg-tertiary) var(--bg-secondary);
}
