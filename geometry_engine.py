#!/usr/bin/env python3
"""
Geometry Engine for 3D Container Generator
Handles creation of boxes, cylinders, prisms with edge modifications.
"""

import trimesh
import numpy as np
from typing import Optional, Tuple, Union

class GeometryEngine:
    """Advanced geometry engine for creating 3D containers with edge modifications."""
    
    def __init__(self):
        self.quality_settings = {
            'low': {'segments': 8, 'resolution': 0.5},
            'medium': {'segments': 16, 'resolution': 0.25},
            'high': {'segments': 32, 'resolution': 0.1},
            'ultra': {'segments': 64, 'resolution': 0.05}
        }
    
    def create_shape(self, shape_type: str, length: float, width: Optional[float], 
                    height: float, wall_thickness: float, edge_type: str = 'sharp',
                    edge_size: float = 0.0, quality: str = 'high') -> trimesh.Trimesh:
        """
        Create a 3D container shape with specified parameters.
        
        Args:
            shape_type: 'box', 'cylinder', or 'prism'
            length: Primary dimension (length for box, diameter for cylinder, side for prism)
            width: Secondary dimension (width for box, ignored for cylinder, sides count for prism)
            height: Height of the container
            wall_thickness: Thickness of the walls
            edge_type: 'sharp', 'rounded', or 'chamfered'
            edge_size: Size of edge modification (radius for rounded, distance for chamfered)
            quality: 'low', 'medium', 'high', or 'ultra'
        
        Returns:
            trimesh.Trimesh: Generated 3D mesh
        """
        # Validate parameters
        self._validate_parameters(shape_type, length, width, height, wall_thickness, edge_size)
        
        # Get quality settings
        quality_config = self.quality_settings.get(quality, self.quality_settings['high'])
        
        # Create base shape
        if shape_type == 'box':
            mesh = self._create_box(length, width, height, wall_thickness, quality_config)
        elif shape_type == 'cylinder':
            mesh = self._create_cylinder(length, height, wall_thickness, quality_config)
        elif shape_type == 'prism':
            sides = int(width) if width else 6  # Default to hexagon
            mesh = self._create_prism(length, sides, height, wall_thickness, quality_config)
        else:
            raise ValueError(f"Unknown shape type: {shape_type}")
        
        # Apply edge modifications
        if edge_type != 'sharp' and edge_size > 0:
            mesh = self._apply_edge_modification(mesh, edge_type, edge_size, quality_config)
        
        return mesh
    
    def _validate_parameters(self, shape_type: str, length: float, width: Optional[float], 
                           height: float, wall_thickness: float, edge_size: float):
        """Validate input parameters."""
        if length <= 0:
            raise ValueError("Length must be positive")
        if width is not None and width <= 0:
            raise ValueError("Width must be positive")
        if height <= 0:
            raise ValueError("Height must be positive")
        if wall_thickness <= 0:
            raise ValueError("Wall thickness must be positive")
        if wall_thickness >= height:
            raise ValueError("Wall thickness cannot exceed height")
        if edge_size < 0:
            raise ValueError("Edge size cannot be negative")
        
        # Shape-specific validations
        if shape_type == 'box':
            if width is None:
                raise ValueError("Width is required for box")
            if wall_thickness * 2 >= min(length, width):
                raise ValueError("Wall thickness too large for box dimensions")
        elif shape_type == 'cylinder':
            if wall_thickness * 2 >= length:  # length is diameter for cylinder
                raise ValueError("Wall thickness too large for cylinder diameter")
        elif shape_type == 'prism':
            if width is not None and width < 3:
                raise ValueError("Prism must have at least 3 sides")
    
    def _create_box(self, length: float, width: float, height: float, 
                   wall_thickness: float, quality_config: dict) -> trimesh.Trimesh:
        """Create a rectangular box container."""
        # Create outer box
        outer_box = trimesh.creation.box(extents=[length, width, height])
        outer_box.apply_translation([length/2, width/2, height/2])
        
        # Create inner cavity
        inner_length = length - 2 * wall_thickness
        inner_width = width - 2 * wall_thickness
        inner_height = height - wall_thickness
        
        if inner_length > 0 and inner_width > 0 and inner_height > 0:
            try:
                inner_box = trimesh.creation.box(extents=[inner_length, inner_width, inner_height])
                inner_box.apply_translation([
                    wall_thickness + inner_length/2,
                    wall_thickness + inner_width/2,
                    wall_thickness + inner_height/2
                ])

                # Create hollow box
                result = outer_box.difference(inner_box)
            except Exception as e:
                print(f"Boolean operation failed for box, using solid: {e}")
                result = outer_box
        else:
            result = outer_box
        
        return result
    
    def _create_cylinder(self, diameter: float, height: float, 
                        wall_thickness: float, quality_config: dict) -> trimesh.Trimesh:
        """Create a cylindrical container."""
        radius = diameter / 2
        segments = quality_config['segments']
        
        # Create outer cylinder
        outer_cylinder = trimesh.creation.cylinder(radius=radius, height=height, sections=segments)
        outer_cylinder.apply_translation([0, 0, height/2])
        
        # Create inner cavity
        inner_radius = radius - wall_thickness
        inner_height = height - wall_thickness
        
        if inner_radius > 0 and inner_height > 0:
            try:
                inner_cylinder = trimesh.creation.cylinder(radius=inner_radius, height=inner_height, sections=segments)
                inner_cylinder.apply_translation([0, 0, wall_thickness + inner_height/2])

                # Create hollow cylinder
                result = outer_cylinder.difference(inner_cylinder)
            except Exception as e:
                print(f"Boolean operation failed for cylinder, using solid: {e}")
                result = outer_cylinder
        else:
            result = outer_cylinder
        
        return result
    
    def _create_prism(self, side_length: float, sides: int, height: float,
                     wall_thickness: float, quality_config: dict) -> trimesh.Trimesh:
        """Create a prismatic container with specified number of sides."""
        # Create regular polygon vertices
        angles = np.linspace(0, 2*np.pi, sides, endpoint=False)
        radius = side_length / (2 * np.sin(np.pi / sides))  # Circumradius

        # Outer polygon vertices
        outer_vertices = []
        for angle in angles:
            x = radius * np.cos(angle)
            y = radius * np.sin(angle)
            outer_vertices.append([x, y])

        outer_vertices = np.array(outer_vertices)

        # Create outer prism using cylinder approximation for reliability
        outer_prism = trimesh.creation.cylinder(radius=radius, height=height, sections=sides)
        outer_prism.apply_translation([0, 0, height/2])
        
        # Create inner cavity
        inner_radius = radius - wall_thickness
        inner_height = height - wall_thickness

        if inner_radius > 0 and inner_height > 0:
            try:
                # Create inner prism using cylinder approximation
                inner_prism = trimesh.creation.cylinder(radius=inner_radius, height=inner_height, sections=sides)
                inner_prism.apply_translation([0, 0, wall_thickness + inner_height/2])

                # Create hollow prism
                result = outer_prism.difference(inner_prism)
            except Exception as e:
                print(f"Boolean operation failed for prism, using solid: {e}")
                result = outer_prism
        else:
            result = outer_prism

        return result
    
    def _apply_edge_modification(self, mesh: trimesh.Trimesh, edge_type: str, 
                               edge_size: float, quality_config: dict) -> trimesh.Trimesh:
        """Apply edge modifications (rounding or chamfering) to the mesh."""
        try:
            if edge_type == 'rounded':
                return self._apply_rounding(mesh, edge_size, quality_config)
            elif edge_type == 'chamfered':
                return self._apply_chamfering(mesh, edge_size, quality_config)
            else:
                return mesh
        except Exception as e:
            print(f"Edge modification failed: {e}, returning original mesh")
            return mesh
    
    def _apply_rounding(self, mesh: trimesh.Trimesh, radius: float, 
                       quality_config: dict) -> trimesh.Trimesh:
        """Apply rounding to mesh edges."""
        # This is a simplified implementation
        # In a full implementation, you would use advanced mesh processing
        # For now, we'll apply a smoothing operation
        
        try:
            # Apply Laplacian smoothing as a simple rounding approximation
            smoothed = mesh.smoothed()
            return smoothed
        except:
            # If smoothing fails, return original mesh
            return mesh
    
    def _apply_chamfering(self, mesh: trimesh.Trimesh, distance: float, 
                         quality_config: dict) -> trimesh.Trimesh:
        """Apply chamfering to mesh edges."""
        # This is a simplified implementation
        # In a full implementation, you would identify edges and create chamfer geometry
        
        try:
            # For now, apply a slight scaling as a chamfer approximation
            scale_factor = 1.0 - (distance * 0.01)  # Very conservative scaling
            scaled_mesh = mesh.copy()
            scaled_mesh.apply_scale(scale_factor)
            return scaled_mesh
        except:
            # If scaling fails, return original mesh
            return mesh
    
    def get_mesh_info(self, mesh: trimesh.Trimesh) -> dict:
        """Get information about the mesh."""
        if mesh is None:
            return {}
        
        try:
            volume = mesh.volume if mesh.is_volume else 0
            surface_area = mesh.area
            bounds = mesh.bounds
            
            return {
                'vertices': len(mesh.vertices),
                'faces': len(mesh.faces),
                'volume': volume,
                'surface_area': surface_area,
                'bounds': bounds,
                'is_watertight': mesh.is_watertight,
                'is_volume': mesh.is_volume
            }
        except Exception as e:
            return {'error': str(e)}
    
    def repair_mesh(self, mesh: trimesh.Trimesh) -> trimesh.Trimesh:
        """Attempt to repair mesh issues."""
        try:
            # Fill holes
            mesh.fill_holes()
            
            # Remove duplicate vertices
            mesh.merge_vertices()
            
            # Remove degenerate faces
            mesh.remove_degenerate_faces()
            
            # Fix normals
            mesh.fix_normals()
            
            return mesh
        except Exception as e:
            print(f"Mesh repair failed: {e}")
            return mesh
