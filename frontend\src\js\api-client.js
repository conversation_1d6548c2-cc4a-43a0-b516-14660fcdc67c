/**
 * API Client for communicating with C++ backend
 */

export class APIClient {
    constructor(baseUrl = 'http://localhost:8081') {
        this.baseUrl = baseUrl;
        this.timeout = 10000; // 10 seconds
    }
    
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        
        // Add timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        config.signal = controller.signal;
        
        try {
            const response = await fetch(url, config);
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.blob();
            }
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            
            throw error;
        }
    }
    
    async getStatus() {
        return await this.request('/api/status');
    }
    
    async generateContainer(params) {
        return await this.request('/api/generate', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }
    
    async getPreview(params) {
        return await this.request('/api/preview', {
            method: 'POST',
            body: JSON.stringify(params)
        });
    }
    
    async exportSTL(params) {
        return await this.request('/api/export', {
            method: 'POST',
            body: JSON.stringify({
                ...params,
                format: 'stl'
            })
        });
    }
    
    async exportOBJ(params) {
        return await this.request('/api/export', {
            method: 'POST',
            body: JSON.stringify({
                ...params,
                format: 'obj'
            })
        });
    }
}
