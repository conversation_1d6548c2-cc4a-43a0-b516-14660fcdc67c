@echo off
REM Start Bambu Studio Style 3D Container Generator (Electron)

echo.
echo 🎯 Starting Bambu Studio Style 3D Container Generator
echo ================================================
echo.

REM Check if we're in the right directory
if not exist "electron-app" (
    echo ❌ Error: electron-app directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

REM Check for Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js found: 
node --version

echo.
echo 🔧 Starting Electron application...

cd electron-app

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Launching desktop application...
echo.
echo 💡 Features available:
echo    ✅ Professional Bambu Studio interface
echo    ✅ Three.js 3D visualization
echo    ✅ Interactive 3D controls
echo    ✅ Shape generation (Box, Cylinder, Prism)
echo    ✅ Real-time parameter adjustment
echo    ✅ STL export functionality
echo    ✅ Native desktop experience
echo.

npm start

echo.
echo 👋 Application closed. Press any key to exit.
pause
