cmake_minimum_required(VERSION 3.16)
project(ContainerGenerator VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages
find_package(OpenGL REQUIRED)
find_package(glfw3 REQUIRED)
find_package(GLEW REQUIRED)
find_package(assimp REQUIRED)

# Include directories
include_directories(include)
include_directories(${OPENGL_INCLUDE_DIRS})

# Source files
set(GEOMETRY_SOURCES
    src/geometry/GeometryEngine.cpp
    src/geometry/BoxGenerator.cpp
    src/geometry/CylinderGenerator.cpp
    src/geometry/PrismGenerator.cpp
    src/geometry/EdgeProcessor.cpp
    src/geometry/MeshUtils.cpp
)

set(OPENGL_SOURCES
    src/opengl/Renderer.cpp
    src/opengl/Shader.cpp
    src/opengl/Camera.cpp
    src/opengl/Scene.cpp
)

set(API_SOURCES
    src/api/Server.cpp
    src/api/RequestHandler.cpp
    src/api/JsonUtils.cpp
)

set(CORE_SOURCES
    src/main.cpp
    src/Application.cpp
)

# Create executable
add_executable(container_generator_server
    ${GEOMETRY_SOURCES}
    ${OPENGL_SOURCES}
    ${API_SOURCES}
    ${CORE_SOURCES}
)

# Link libraries
target_link_libraries(container_generator_server
    ${OPENGL_LIBRARIES}
    glfw
    GLEW::GLEW
    assimp::assimp
    pthread
)

# Compiler flags
target_compile_options(container_generator_server PRIVATE
    -Wall -Wextra -O3
)

# Install
install(TARGETS container_generator_server
    DESTINATION bin
)
