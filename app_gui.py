import tkinter as tk
from tkinter import messagebox, filedialog
from kotak_generator import buat_kotak_berdinding, simpan_stl

from vedo import Plotter, Mesh

class KotakApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Kotak 3D Generator")

        # Frame kiri untuk input
        self.input_frame = tk.Frame(root)
        self.input_frame.pack(padx=20, pady=20)

        tk.Label(self.input_frame, text="Panjang (mm):").grid(row=0, column=0, sticky="e")
        self.entry_panjang = tk.Entry(self.input_frame)
        self.entry_panjang.grid(row=0, column=1)

        tk.Label(self.input_frame, text="Lebar (mm):").grid(row=1, column=0, sticky="e")
        self.entry_lebar = tk.Entry(self.input_frame)
        self.entry_lebar.grid(row=1, column=1)

        tk.Label(self.input_frame, text="Tinggi (mm):").grid(row=2, column=0, sticky="e")
        self.entry_tinggi = tk.Entry(self.input_frame)
        self.entry_tinggi.grid(row=2, column=1)

        tk.Label(self.input_frame, text="<PERSON><PERSON><PERSON><PERSON> (mm):").grid(row=3, column=0, sticky="e")
        self.entry_ketebalan = tk.Entry(self.input_frame)
        self.entry_ketebalan.grid(row=3, column=1)

        # Tombol-tombol
        tk.Button(self.input_frame, text="Preview 3D", command=self.preview_3d).grid(row=4, column=0, pady=10)
        tk.Button(self.input_frame, text="Generate STL", command=self.generate_stl).grid(row=4, column=1, pady=10)

    def get_parameters(self):
        return (
            float(self.entry_panjang.get()),
            float(self.entry_lebar.get()),
            float(self.entry_tinggi.get()),
            float(self.entry_ketebalan.get())
        )

    def preview_3d(self):
        try:
            panjang, lebar, tinggi, ketebalan = self.get_parameters()
            mesh = buat_kotak_berdinding(panjang, lebar, tinggi, ketebalan)
            vmesh = Mesh([mesh.vertices, mesh.faces])
            vmesh.color("lightblue").alpha(0.7).lighting("plastic")

            plt = Plotter(title="Preview Kotak 3D", axes=1)
            plt.show(vmesh, "Klik dan drag untuk rotasi", interactive=True)

        except Exception as e:
            messagebox.showerror("Error", str(e))

    def generate_stl(self):
        try:
            panjang, lebar, tinggi, ketebalan = self.get_parameters()
            mesh = buat_kotak_berdinding(panjang, lebar, tinggi, ketebalan)

            filepath = filedialog.asksaveasfilename(
                defaultextension=".stl",
                filetypes=[("STL files", "*.stl")],
                title="Simpan file STL"
            )
            if filepath:
                simpan_stl(mesh, filepath)
                messagebox.showinfo("Berhasil", f"File STL disimpan:\n{filepath}")
        except Exception as e:
            messagebox.showerror("Error", str(e))

# Jalankan aplikasi
if __name__ == "__main__":
    root = tk.Tk()
    app = KotakApp(root)
    root.mainloop()
