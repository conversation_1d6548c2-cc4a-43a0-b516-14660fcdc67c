#pragma once

#include <GL/glew.h>
#include <GLFW/glfw3.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct Mesh;
class Shader;
class Camera;
class Scene;

/**
 * Professional OpenGL renderer for 3D container preview
 */
class Renderer {
public:
    Renderer();
    ~Renderer();

    // Initialization
    bool Initialize(int width, int height, bool headless = false);
    void Shutdown();

    // Rendering
    void BeginFrame();
    void EndFrame();
    void Clear();
    
    // Mesh rendering
    void RenderMesh(const Mesh& mesh);
    void RenderWireframe(const Mesh& mesh);
    void RenderPoints(const Mesh& mesh);
    
    // Scene management
    void SetCamera(std::shared_ptr<Camera> camera);
    void SetScene(std::shared_ptr<Scene> scene);
    
    // View modes
    enum ViewMode { SOLID, WIREFRAME, POINTS, XRAY };
    void SetViewMode(ViewMode mode) { viewMode_ = mode; }
    ViewMode GetViewMode() const { return viewMode_; }
    
    // Lighting
    struct Light {
        float position[3];
        float color[3];
        float intensity;
        bool enabled;
    };
    
    void SetLight(int index, const Light& light);
    void EnableLighting(bool enable) { lightingEnabled_ = enable; }
    
    // Material properties
    struct Material {
        float ambient[3] = {0.2f, 0.2f, 0.8f};
        float diffuse[3] = {0.4f, 0.4f, 1.0f};
        float specular[3] = {1.0f, 1.0f, 1.0f};
        float shininess = 50.0f;
    };
    
    void SetMaterial(const Material& material) { material_ = material; }
    
    // Viewport
    void SetViewport(int x, int y, int width, int height);
    void GetViewport(int& x, int& y, int& width, int& height) const;
    
    // Frame capture
    bool CaptureFrame(std::vector<unsigned char>& pixels, int& width, int& height);
    bool SaveScreenshot(const std::string& filename);
    
    // Statistics
    struct RenderStats {
        int frameCount = 0;
        float fps = 0.0f;
        int verticesRendered = 0;
        int trianglesRendered = 0;
        float frameTime = 0.0f;
    };
    
    const RenderStats& GetStats() const { return stats_; }
    
    // Window management
    GLFWwindow* GetWindow() const { return window_; }
    bool ShouldClose() const;
    void SwapBuffers();
    void PollEvents();

private:
    // OpenGL context
    GLFWwindow* window_;
    int windowWidth_, windowHeight_;
    bool headless_;
    
    // Rendering components
    std::shared_ptr<Camera> camera_;
    std::shared_ptr<Scene> scene_;
    std::unique_ptr<Shader> meshShader_;
    std::unique_ptr<Shader> wireframeShader_;
    
    // Rendering state
    ViewMode viewMode_;
    bool lightingEnabled_;
    Material material_;
    std::vector<Light> lights_;
    
    // OpenGL objects
    GLuint meshVAO_, meshVBO_, meshEBO_;
    GLuint wireframeVAO_, wireframeVBO_, wireframeEBO_;
    
    // Statistics
    RenderStats stats_;
    double lastFrameTime_;
    
    // Initialization helpers
    bool InitializeGLFW();
    bool InitializeGLEW();
    bool InitializeShaders();
    bool InitializeBuffers();
    
    // Mesh upload
    void UploadMesh(const Mesh& mesh);
    void UpdateBuffers(const Mesh& mesh);
    
    // Shader management
    void UseShader(const Shader& shader);
    void SetUniforms();
    
    // Error handling
    void CheckGLError(const std::string& operation);
    static void GLFWErrorCallback(int error, const char* description);
};
