<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Container Generator - Bambu Studio Style</title>
    
    <!-- Three.js -->
    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.155.0/examples/js/controls/OrbitControls.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2b2b2b;
            padding: 20px;
            border-right: 1px solid #404040;
        }
        
        .viewport {
            flex: 1;
            position: relative;
        }
        
        #canvas3d {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .controls h3 {
            color: #4A90E2;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .shape-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #404040;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .shape-btn:hover {
            background: #4A90E2;
        }
        
        .shape-btn.active {
            background: #4A90E2;
        }
        
        .param-group {
            margin-bottom: 15px;
        }
        
        .param-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #cccccc;
        }
        
        .param-group input {
            width: 100%;
            padding: 5px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 3px;
            color: white;
        }
        
        .export-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4A90E2, #FF6B35);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }
        
        .export-btn:hover {
            opacity: 0.9;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="controls">
                <h3>🔧 Shape Tools</h3>
                <button class="shape-btn active" onclick="selectShape('box')">📦 Box</button>
                <button class="shape-btn" onclick="selectShape('cylinder')">🥫 Cylinder</button>
                <button class="shape-btn" onclick="selectShape('prism')">🔺 Prism</button>
            </div>
            
            <div class="controls">
                <h3>📏 Dimensions</h3>
                <div class="param-group">
                    <label>Length/Diameter (mm)</label>
                    <input type="number" id="length" value="100" min="1" max="1000" onchange="updateGeometry()">
                </div>
                <div class="param-group">
                    <label>Width (mm)</label>
                    <input type="number" id="width" value="60" min="1" max="1000" onchange="updateGeometry()">
                </div>
                <div class="param-group">
                    <label>Height (mm)</label>
                    <input type="number" id="height" value="40" min="1" max="1000" onchange="updateGeometry()">
                </div>
                <div class="param-group">
                    <label>Wall Thickness (mm)</label>
                    <input type="number" id="thickness" value="3" min="0.1" max="50" step="0.1" onchange="updateGeometry()">
                </div>
            </div>
            
            <div class="controls">
                <h3>⚙️ Tools</h3>
                <button class="shape-btn" onclick="resetView()">🔄 Reset View</button>
                <button class="shape-btn" onclick="fitView()">📐 Fit View</button>
            </div>
            
            <button class="export-btn" onclick="exportSTL()">💾 Export STL</button>
        </div>
        
        <div class="viewport">
            <canvas id="canvas3d"></canvas>
            <div class="status" id="status">Ready</div>
        </div>
    </div>

    <script>
        // Global variables
        let scene, camera, renderer, controls;
        let currentMesh = null;
        let currentShape = 'box';
        
        // Initialize Three.js
        function init() {
            console.log('🎯 Initializing 3D Container Generator');
            
            // Check Three.js
            if (typeof THREE === 'undefined') {
                updateStatus('❌ Three.js not loaded');
                return;
            }
            
            updateStatus('🔧 Initializing 3D viewport...');
            
            // Get canvas
            const canvas = document.getElementById('canvas3d');
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0f0f0f);
            
            // Create camera
            camera = new THREE.PerspectiveCamera(45, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            camera.position.set(150, 100, 150);
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // Create controls
            controls = new THREE.OrbitControls(camera, canvas);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Add grid
            const gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
            scene.add(gridHelper);
            
            // Add axes
            const axesHelper = new THREE.AxesHelper(50);
            scene.add(axesHelper);
            
            // Handle resize
            window.addEventListener('resize', onWindowResize);
            
            // Generate initial geometry
            updateGeometry();
            
            // Start render loop
            animate();
            
            updateStatus('✅ Ready - Click shapes to generate containers');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        
        function onWindowResize() {
            const canvas = document.getElementById('canvas3d');
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        }
        
        function selectShape(shape) {
            currentShape = shape;
            
            // Update button states
            document.querySelectorAll('.shape-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Show/hide width input for cylinder
            const widthInput = document.getElementById('width').parentElement;
            widthInput.style.display = shape === 'cylinder' ? 'none' : 'block';
            
            updateGeometry();
        }
        
        function updateGeometry() {
            updateStatus('🔧 Generating geometry...');
            
            // Remove existing mesh
            if (currentMesh) {
                scene.remove(currentMesh);
                currentMesh.geometry.dispose();
                currentMesh.material.dispose();
            }
            
            // Get parameters
            const length = parseFloat(document.getElementById('length').value);
            const width = parseFloat(document.getElementById('width').value);
            const height = parseFloat(document.getElementById('height').value);
            const thickness = parseFloat(document.getElementById('thickness').value);
            
            // Create geometry
            let geometry;
            
            try {
                switch (currentShape) {
                    case 'box':
                        geometry = createBoxGeometry(length, width, height, thickness);
                        break;
                    case 'cylinder':
                        geometry = createCylinderGeometry(length, height, thickness);
                        break;
                    case 'prism':
                        geometry = createPrismGeometry(length, height, thickness, 6);
                        break;
                    default:
                        geometry = new THREE.BoxGeometry(length, width, height);
                }
                
                // Create material
                const material = new THREE.MeshPhongMaterial({
                    color: 0x4A90E2,
                    shininess: 100
                });
                
                // Create mesh
                currentMesh = new THREE.Mesh(geometry, material);
                currentMesh.castShadow = true;
                currentMesh.receiveShadow = true;
                
                // Add to scene
                scene.add(currentMesh);
                
                updateStatus(`✅ ${currentShape} generated - ${geometry.attributes.position.count} vertices`);
                
            } catch (error) {
                console.error('Geometry generation failed:', error);
                updateStatus('❌ Geometry generation failed');
            }
        }
        
        function createBoxGeometry(length, width, height, thickness) {
            // Simple box for now - can be enhanced with hollow interior
            return new THREE.BoxGeometry(length, width, height);
        }
        
        function createCylinderGeometry(diameter, height, thickness) {
            const radius = diameter / 2;
            return new THREE.CylinderGeometry(radius, radius, height, 32);
        }
        
        function createPrismGeometry(sideLength, height, thickness, sides) {
            const radius = sideLength / (2 * Math.sin(Math.PI / sides));
            return new THREE.CylinderGeometry(radius, radius, height, sides);
        }
        
        function resetView() {
            camera.position.set(150, 100, 150);
            camera.lookAt(0, 0, 0);
            controls.reset();
            updateStatus('🔄 View reset');
        }
        
        function fitView() {
            if (currentMesh) {
                const box = new THREE.Box3().setFromObject(currentMesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                const maxDim = Math.max(size.x, size.y, size.z);
                const distance = maxDim * 2;
                
                camera.position.copy(center);
                camera.position.x += distance;
                camera.position.y += distance * 0.5;
                camera.position.z += distance;
                camera.lookAt(center);
                
                controls.target.copy(center);
                controls.update();
                
                updateStatus('📐 View fitted to object');
            }
        }
        
        function exportSTL() {
            if (!currentMesh) {
                updateStatus('❌ No geometry to export');
                return;
            }
            
            updateStatus('💾 Exporting STL...');
            
            try {
                // Simple STL export
                const geometry = currentMesh.geometry;
                const vertices = geometry.attributes.position.array;
                
                let stl = 'solid container\n';
                
                for (let i = 0; i < vertices.length; i += 9) {
                    const v1 = [vertices[i], vertices[i+1], vertices[i+2]];
                    const v2 = [vertices[i+3], vertices[i+4], vertices[i+5]];
                    const v3 = [vertices[i+6], vertices[i+7], vertices[i+8]];
                    
                    stl += `  facet normal 0 0 1\n`;
                    stl += `    outer loop\n`;
                    stl += `      vertex ${v1[0]} ${v1[1]} ${v1[2]}\n`;
                    stl += `      vertex ${v2[0]} ${v2[1]} ${v2[2]}\n`;
                    stl += `      vertex ${v3[0]} ${v3[1]} ${v3[2]}\n`;
                    stl += `    endloop\n`;
                    stl += `  endfacet\n`;
                }
                
                stl += 'endsolid container\n';
                
                // Download file
                const blob = new Blob([stl], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'container.stl';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                updateStatus('✅ STL exported successfully');
                
            } catch (error) {
                console.error('STL export failed:', error);
                updateStatus('❌ STL export failed');
            }
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
