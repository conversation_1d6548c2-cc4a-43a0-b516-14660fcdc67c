/**
 * Main Application Entry Point
 * Bambu Studio Style 3D Container Generator
 */

import { Viewport3D } from "./viewport3d.js";
import { APIClient } from "./api-client.js";
import { GeometryManager } from "./geometry-manager.js";

class ContainerGeneratorApp {
  constructor() {
    this.viewport = null;
    this.apiClient = null;
    this.geometryManager = null;

    // Application state
    this.currentShape = "box";
    this.currentParameters = {
      shapeType: "box",
      length: 100,
      width: 60,
      height: 40,
      wallThickness: 3,
      edgeType: "sharp",
      edgeSize: 0,
      segments: 32,
      sides: 6,
      quality: "high",
    };

    this.isGenerating = false;
    this.currentMesh = null;
  }

  async initialize() {
    console.log("🎯 Initializing Container Generator App");

    try {
      // Initialize API client
      this.apiClient = new APIClient("http://localhost:8081");

      // Initialize 3D viewport
      this.viewport = new Viewport3D("viewport3d");
      await this.viewport.initialize();

      // UI is handled directly in this class

      // Initialize geometry manager
      this.geometryManager = new GeometryManager(this.apiClient);

      // Setup event listeners
      this.setupEventListeners();

      // Check backend connection
      await this.checkBackendConnection();

      // Generate initial container
      await this.generateContainer();

      console.log("✅ Application initialized successfully");
      this.updateStatus("Ready", "success");
    } catch (error) {
      console.error("❌ Failed to initialize application:", error);
      this.updateStatus("Initialization failed", "error");
      throw error;
    }
  }

  setupEventListeners() {
    // Shape selection
    document.querySelectorAll(".shape-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const shape = e.currentTarget.dataset.shape;
        this.selectShape(shape);
      });
    });

    // View mode controls
    document.querySelectorAll(".view-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const viewMode = e.currentTarget.dataset.view;
        this.setViewMode(viewMode);
      });
    });

    // Parameter inputs
    const parameterInputs = [
      "lengthInput",
      "widthInput",
      "heightInput",
      "thicknessInput",
      "edgeSizeInput",
      "segmentsInput",
    ];

    parameterInputs.forEach((inputId) => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener("input", () => this.onParameterChange());
        input.addEventListener("change", () => this.onParameterChange());
      }
    });

    // Select inputs
    const selectInputs = ["edgeTypeSelect", "qualitySelect"];
    selectInputs.forEach((selectId) => {
      const select = document.getElementById(selectId);
      if (select) {
        select.addEventListener("change", () => this.onParameterChange());
      }
    });

    // Toolbar buttons
    document
      .getElementById("newBtn")
      ?.addEventListener("click", () => this.newProject());
    document
      .getElementById("exportBtn")
      ?.addEventListener("click", () => this.exportSTL());
    document
      .getElementById("resetViewBtn")
      ?.addEventListener("click", () => this.resetView());
    document
      .getElementById("fitViewBtn")
      ?.addEventListener("click", () => this.fitView());

    // Edge tools
    document
      .getElementById("roundEdgesBtn")
      ?.addEventListener("click", () => this.applyRoundEdges());
    document
      .getElementById("chamferEdgesBtn")
      ?.addEventListener("click", () => this.applyChamferEdges());
    document
      .getElementById("resetEdgesBtn")
      ?.addEventListener("click", () => this.resetEdges());

    // Tab switching
    document.querySelectorAll(".tab-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const tabName = e.currentTarget.dataset.tab;
        this.switchTab(tabName);
      });
    });

    // Viewport controls
    document
      .getElementById("gridToggle")
      ?.addEventListener("click", () => this.toggleGrid());
    document
      .getElementById("axesToggle")
      ?.addEventListener("click", () => this.toggleAxes());

    // Export buttons
    document
      .getElementById("exportSTLBtn")
      ?.addEventListener("click", () => this.exportSTL());
    document
      .getElementById("exportOBJBtn")
      ?.addEventListener("click", () => this.exportOBJ());

    // Material color picker
    document.getElementById("colorPicker")?.addEventListener("change", (e) => {
      this.viewport.setMaterialColor(e.target.value);
    });
  }

  async checkBackendConnection() {
    try {
      this.updateStatus("Connecting to backend...", "loading");
      const status = await this.apiClient.getStatus();
      console.log("✅ Backend connection established:", status);
    } catch (error) {
      console.warn("⚠️ Backend not available, using frontend-only mode");
      this.updateStatus("Backend offline - Frontend mode", "warning");
    }
  }

  selectShape(shape) {
    this.currentShape = shape;
    this.currentParameters.shapeType = shape;

    // Update UI
    document.querySelectorAll(".shape-btn").forEach((btn) => {
      btn.classList.toggle("active", btn.dataset.shape === shape);
    });

    // Update parameter visibility
    this.updateParameterVisibility();

    // Regenerate container
    this.generateContainer();
  }

  updateParameterVisibility() {
    const widthInput = document.getElementById("widthInput");
    const widthRow = widthInput?.closest(".property-row");

    if (widthRow) {
      // Hide width for cylinder, show for others
      widthRow.style.display =
        this.currentShape === "cylinder" ? "none" : "flex";
    }

    const segmentsInput = document.getElementById("segmentsInput");
    const segmentsRow = segmentsInput?.closest(".property-row");

    if (segmentsRow) {
      // Show segments for cylinder and prism
      segmentsRow.style.display = ["cylinder", "prism"].includes(
        this.currentShape
      )
        ? "flex"
        : "none";
    }
  }

  setViewMode(mode) {
    // Update UI
    document.querySelectorAll(".view-btn").forEach((btn) => {
      btn.classList.toggle("active", btn.dataset.view === mode);
    });

    // Update viewport
    this.viewport.setViewMode(mode);
  }

  onParameterChange() {
    // Debounce parameter changes
    if (this.parameterTimeout) {
      clearTimeout(this.parameterTimeout);
    }

    this.parameterTimeout = setTimeout(() => {
      this.updateParameters();
      this.generateContainer();
    }, 300);
  }

  updateParameters() {
    // Get values from UI
    this.currentParameters = {
      shapeType: this.currentShape,
      length: parseFloat(document.getElementById("lengthInput")?.value || 100),
      width: parseFloat(document.getElementById("widthInput")?.value || 60),
      height: parseFloat(document.getElementById("heightInput")?.value || 40),
      wallThickness: parseFloat(
        document.getElementById("thicknessInput")?.value || 3
      ),
      edgeType: document.getElementById("edgeTypeSelect")?.value || "sharp",
      edgeSize: parseFloat(
        document.getElementById("edgeSizeInput")?.value || 0
      ),
      segments: parseInt(document.getElementById("segmentsInput")?.value || 32),
      sides: 6, // For prisms
      quality: document.getElementById("qualitySelect")?.value || "high",
    };

    // Validate parameters
    this.validateParameters();
  }

  validateParameters() {
    const params = this.currentParameters;

    // Basic validation
    if (params.length <= 0) params.length = 1;
    if (params.width <= 0) params.width = 1;
    if (params.height <= 0) params.height = 1;
    if (params.wallThickness <= 0) params.wallThickness = 0.1;
    if (params.edgeSize < 0) params.edgeSize = 0;
    if (params.segments < 6) params.segments = 6;

    // Shape-specific validation
    if (params.shapeType === "cylinder") {
      if (params.wallThickness * 2 >= params.length) {
        params.wallThickness = params.length * 0.4;
      }
    } else {
      if (params.wallThickness * 2 >= Math.min(params.length, params.width)) {
        params.wallThickness = Math.min(params.length, params.width) * 0.4;
      }
    }

    if (params.wallThickness >= params.height) {
      params.wallThickness = params.height * 0.8;
    }
  }

  async generateContainer() {
    if (this.isGenerating) return;

    this.isGenerating = true;
    this.updateStatus("Generating container...", "loading");
    this.showLoadingSpinner(true);

    try {
      // Try backend first, fallback to frontend
      let mesh;
      try {
        mesh = await this.geometryManager.generateContainer(
          this.currentParameters
        );
      } catch (error) {
        console.warn("Backend generation failed, using frontend fallback");
        mesh = await this.geometryManager.generateContainerFrontend(
          this.currentParameters
        );
      }

      if (mesh) {
        this.currentMesh = mesh;
        await this.viewport.loadMesh(mesh);
        this.updateModelInfo(mesh);
        this.updateStatus(
          `Container generated - ${mesh.faces?.length || 0} faces`,
          "success"
        );
      } else {
        throw new Error("Failed to generate mesh");
      }
    } catch (error) {
      console.error("❌ Container generation failed:", error);
      this.updateStatus("Generation failed", "error");
    } finally {
      this.isGenerating = false;
      this.showLoadingSpinner(false);
    }
  }

  updateModelInfo(mesh) {
    if (!mesh) return;

    const vertexCount = mesh.vertices?.length || 0;
    const faceCount = mesh.faces?.length || 0;
    const volume = mesh.volume || 0;

    document.getElementById("vertexCount").textContent =
      vertexCount.toLocaleString();
    document.getElementById("faceCount").textContent =
      faceCount.toLocaleString();
    document.getElementById("volumeValue").textContent = `${volume.toFixed(
      2
    )} cm³`;
  }

  updateStatus(message, type = "info") {
    const statusText = document.getElementById("statusText");
    const statusDot = document.getElementById("statusDot");

    if (statusText) statusText.textContent = message;
    if (statusDot) {
      statusDot.className = `status-dot ${type}`;
    }
  }

  showLoadingSpinner(show) {
    const spinner = document.getElementById("loadingSpinner");
    if (spinner) {
      spinner.style.display = show ? "flex" : "none";
    }
  }

  // Tool actions
  applyRoundEdges() {
    document.getElementById("edgeTypeSelect").value = "rounded";
    if (document.getElementById("edgeSizeInput").value === "0") {
      document.getElementById("edgeSizeInput").value = "2";
    }
    this.onParameterChange();
  }

  applyChamferEdges() {
    document.getElementById("edgeTypeSelect").value = "chamfered";
    if (document.getElementById("edgeSizeInput").value === "0") {
      document.getElementById("edgeSizeInput").value = "2";
    }
    this.onParameterChange();
  }

  resetEdges() {
    document.getElementById("edgeTypeSelect").value = "sharp";
    document.getElementById("edgeSizeInput").value = "0";
    this.onParameterChange();
  }

  // View controls
  resetView() {
    this.viewport.resetView();
  }

  fitView() {
    this.viewport.fitView();
  }

  toggleGrid() {
    this.viewport.toggleGrid();
  }

  toggleAxes() {
    this.viewport.toggleAxes();
  }

  // Tab management
  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll(".tab-btn").forEach((btn) => {
      btn.classList.toggle("active", btn.dataset.tab === tabName);
    });

    // Update tab content
    document.querySelectorAll(".tab-content").forEach((content) => {
      content.classList.toggle("active", content.id === `${tabName}Tab`);
    });
  }

  // Project management
  newProject() {
    // Reset to defaults
    this.currentParameters = {
      shapeType: "box",
      length: 100,
      width: 60,
      height: 40,
      wallThickness: 3,
      edgeType: "sharp",
      edgeSize: 0,
      segments: 32,
      sides: 6,
      quality: "high",
    };

    // Update UI
    this.selectShape("box");
    this.updateUIFromParameters();
    this.generateContainer();
  }

  updateUIFromParameters() {
    const params = this.currentParameters;

    document.getElementById("lengthInput").value = params.length;
    document.getElementById("widthInput").value = params.width;
    document.getElementById("heightInput").value = params.height;
    document.getElementById("thicknessInput").value = params.wallThickness;
    document.getElementById("edgeTypeSelect").value = params.edgeType;
    document.getElementById("edgeSizeInput").value = params.edgeSize;
    document.getElementById("segmentsInput").value = params.segments;
    document.getElementById("qualitySelect").value = params.quality;
  }

  // Export functions
  async exportSTL() {
    if (!this.currentMesh) {
      this.updateStatus("No model to export", "error");
      return;
    }

    try {
      this.updateStatus("Exporting STL...", "loading");

      // Try backend export first
      try {
        const blob = await this.apiClient.exportSTL(this.currentParameters);
        this.downloadFile(blob, "container.stl");
      } catch (error) {
        // Fallback to frontend export
        const stlData = this.geometryManager.exportSTL(this.currentMesh);
        const blob = new Blob([stlData], { type: "application/octet-stream" });
        this.downloadFile(blob, "container.stl");
      }

      this.updateStatus("STL exported successfully", "success");
    } catch (error) {
      console.error("❌ STL export failed:", error);
      this.updateStatus("Export failed", "error");
    }
  }

  async exportOBJ() {
    if (!this.currentMesh) {
      this.updateStatus("No model to export", "error");
      return;
    }

    try {
      this.updateStatus("Exporting OBJ...", "loading");

      const objData = this.geometryManager.exportOBJ(this.currentMesh);
      const blob = new Blob([objData], { type: "text/plain" });
      this.downloadFile(blob, "container.obj");

      this.updateStatus("OBJ exported successfully", "success");
    } catch (error) {
      console.error("❌ OBJ export failed:", error);
      this.updateStatus("Export failed", "error");
    }
  }

  downloadFile(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Initialize application when DOM is loaded
document.addEventListener("DOMContentLoaded", async () => {
  try {
    const app = new ContainerGeneratorApp();
    await app.initialize();

    // Make app globally available for debugging
    window.containerApp = app;
  } catch (error) {
    console.error("❌ Failed to start application:", error);

    // Show error message to user
    const statusText = document.getElementById("statusText");
    if (statusText) {
      statusText.textContent = "Application failed to start";
    }
  }
});
