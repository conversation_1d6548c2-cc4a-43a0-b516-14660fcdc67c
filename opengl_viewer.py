#!/usr/bin/env python3
"""
OpenGL 3D Viewer for Bambu Studio Style Application
Professional 3D viewport with modern rendering capabilities.
"""

import tkinter as tk
import numpy as np
import math

# OpenGL imports with fallback
try:
    from OpenGL.GL import *
    from OpenGL.GLU import *
    from OpenGL.arrays import vbo
    try:
        import tkinter.opengl as togl
        OPENGL_AVAILABLE = True
        OPENGL_METHOD = "tkinter"
    except ImportError:
        OPENGL_AVAILABLE = False
        OPENGL_METHOD = None
except ImportError:
    OPENGL_AVAILABLE = False
    OPENGL_METHOD = None

class OpenGLViewer(tk.Frame):
    """Professional OpenGL 3D viewer widget."""
    
    def __init__(self, parent, width=800, height=600, bg_color=(0.15, 0.15, 0.15)):
        super().__init__(parent, bg='#2b2b2b')
        
        self.width = width
        self.height = height
        self.bg_color = bg_color
        
        # Camera and interaction state
        self.camera_distance = 5.0
        self.camera_rotation_x = 20.0
        self.camera_rotation_y = 45.0
        self.camera_target = [0.0, 0.0, 0.0]
        
        # Mouse interaction
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.mouse_dragging = False
        self.mouse_button = None
        
        # Rendering state
        self.mesh_data = None
        self.view_mode = "solid"  # solid, wireframe, x-ray
        self.show_grid = True
        self.show_axes = True
        
        # Lighting
        self.light_position = [2.0, 2.0, 2.0, 1.0]
        self.light_ambient = [0.3, 0.3, 0.3, 1.0]
        self.light_diffuse = [0.8, 0.8, 0.8, 1.0]
        self.light_specular = [1.0, 1.0, 1.0, 1.0]
        
        # Material properties
        self.material_ambient = [0.2, 0.2, 0.8, 1.0]
        self.material_diffuse = [0.4, 0.4, 1.0, 1.0]
        self.material_specular = [1.0, 1.0, 1.0, 1.0]
        self.material_shininess = 50.0
        
        self.setup_viewer()
    
    def setup_viewer(self):
        """Setup the OpenGL viewer."""
        if OPENGL_AVAILABLE and OPENGL_METHOD == "tkinter":
            self.setup_opengl_widget()
        else:
            self.setup_fallback_widget()
    
    def setup_opengl_widget(self):
        """Setup OpenGL widget using tkinter.opengl."""
        try:
            # Create OpenGL widget
            self.opengl_widget = togl.Opengl(self, width=self.width, height=self.height, double=1)
            self.opengl_widget.pack(fill=tk.BOTH, expand=True)
            
            # Bind events
            self.opengl_widget.bind('<Button-1>', self.on_mouse_press)
            self.opengl_widget.bind('<Button-2>', self.on_mouse_press)
            self.opengl_widget.bind('<Button-3>', self.on_mouse_press)
            self.opengl_widget.bind('<B1-Motion>', self.on_mouse_drag)
            self.opengl_widget.bind('<B2-Motion>', self.on_mouse_drag)
            self.opengl_widget.bind('<B3-Motion>', self.on_mouse_drag)
            self.opengl_widget.bind('<ButtonRelease-1>', self.on_mouse_release)
            self.opengl_widget.bind('<ButtonRelease-2>', self.on_mouse_release)
            self.opengl_widget.bind('<ButtonRelease-3>', self.on_mouse_release)
            self.opengl_widget.bind('<MouseWheel>', self.on_mouse_wheel)
            self.opengl_widget.bind('<Configure>', self.on_resize)
            
            # Initialize OpenGL
            self.opengl_widget.tkMakeCurrent()
            self.init_opengl()
            
            # Initial render
            self.render()
            
            print("✅ OpenGL viewer initialized successfully")
            
        except Exception as e:
            print(f"OpenGL widget setup failed: {e}")
            self.setup_fallback_widget()
    
    def setup_fallback_widget(self):
        """Setup fallback widget when OpenGL is not available."""
        fallback_frame = tk.Frame(self, bg='#404040', relief=tk.SUNKEN, bd=2)
        fallback_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Fallback content
        info_label = tk.Label(fallback_frame, 
                             text="3D Viewport\n\n⚠️ OpenGL not available\n\nMesh information will be\ndisplayed here when loaded",
                             bg='#404040', fg='white', font=('Arial', 12), justify=tk.CENTER)
        info_label.pack(expand=True)
        
        self.fallback_label = info_label
        print("⚠️ Using fallback viewer (OpenGL not available)")
    
    def init_opengl(self):
        """Initialize OpenGL settings."""
        # Enable depth testing
        glEnable(GL_DEPTH_TEST)
        glDepthFunc(GL_LESS)
        
        # Enable face culling
        glEnable(GL_CULL_FACE)
        glCullFace(GL_BACK)
        glFrontFace(GL_CCW)
        
        # Enable lighting
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        glEnable(GL_COLOR_MATERIAL)
        glColorMaterial(GL_FRONT_AND_BACK, GL_AMBIENT_AND_DIFFUSE)
        
        # Set light properties
        glLightfv(GL_LIGHT0, GL_POSITION, self.light_position)
        glLightfv(GL_LIGHT0, GL_AMBIENT, self.light_ambient)
        glLightfv(GL_LIGHT0, GL_DIFFUSE, self.light_diffuse)
        glLightfv(GL_LIGHT0, GL_SPECULAR, self.light_specular)
        
        # Set material properties
        glMaterialfv(GL_FRONT, GL_AMBIENT, self.material_ambient)
        glMaterialfv(GL_FRONT, GL_DIFFUSE, self.material_diffuse)
        glMaterialfv(GL_FRONT, GL_SPECULAR, self.material_specular)
        glMaterialf(GL_FRONT, GL_SHININESS, self.material_shininess)
        
        # Enable smooth shading
        glShadeModel(GL_SMOOTH)
        
        # Set background color
        glClearColor(*self.bg_color, 1.0)
        
        # Setup initial viewport
        self.setup_viewport()
    
    def setup_viewport(self):
        """Setup OpenGL viewport and projection."""
        glViewport(0, 0, self.width, self.height)
        
        # Setup projection matrix
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        
        aspect_ratio = self.width / self.height if self.height > 0 else 1.0
        gluPerspective(45.0, aspect_ratio, 0.1, 100.0)
        
        # Switch to modelview matrix
        glMatrixMode(GL_MODELVIEW)
    
    def load_mesh(self, mesh):
        """Load a trimesh object for display."""
        if not OPENGL_AVAILABLE:
            # Update fallback display
            if hasattr(self, 'fallback_label'):
                info_text = f"3D Viewport\n\nMesh Loaded:\n{len(mesh.vertices)} vertices\n{len(mesh.faces)} faces\n\nOpenGL required for\n3D visualization"
                self.fallback_label.config(text=info_text)
            return
        
        try:
            # Extract mesh data
            vertices = mesh.vertices.astype(np.float32)
            faces = mesh.faces.astype(np.uint32)
            
            # Calculate normals if not available
            if hasattr(mesh, 'vertex_normals'):
                normals = mesh.vertex_normals.astype(np.float32)
            else:
                # Calculate face normals and convert to vertex normals
                mesh.vertex_normals  # This computes them
                normals = mesh.vertex_normals.astype(np.float32)
            
            # Store mesh data
            self.mesh_data = {
                'vertices': vertices,
                'faces': faces,
                'normals': normals,
                'bounds': mesh.bounds
            }
            
            # Auto-fit view to mesh
            self.fit_view()
            
            # Render
            self.render()
            
        except Exception as e:
            print(f"Failed to load mesh: {e}")
    
    def render(self):
        """Render the 3D scene."""
        if not OPENGL_AVAILABLE or not hasattr(self, 'opengl_widget'):
            return
        
        try:
            self.opengl_widget.tkMakeCurrent()
            
            # Clear buffers
            glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
            
            # Setup camera
            glLoadIdentity()
            self.setup_camera()
            
            # Render grid if enabled
            if self.show_grid:
                self.render_grid()
            
            # Render axes if enabled
            if self.show_axes:
                self.render_axes()
            
            # Render mesh if loaded
            if self.mesh_data is not None:
                self.render_mesh()
            
            # Swap buffers
            self.opengl_widget.tkSwapBuffers()
            
        except Exception as e:
            print(f"Render error: {e}")
    
    def setup_camera(self):
        """Setup camera transformation."""
        # Translate to camera distance
        glTranslatef(0.0, 0.0, -self.camera_distance)
        
        # Apply rotations
        glRotatef(self.camera_rotation_x, 1.0, 0.0, 0.0)
        glRotatef(self.camera_rotation_y, 0.0, 1.0, 0.0)
        
        # Translate to target
        glTranslatef(-self.camera_target[0], -self.camera_target[1], -self.camera_target[2])
    
    def render_grid(self):
        """Render reference grid."""
        glDisable(GL_LIGHTING)
        glColor3f(0.3, 0.3, 0.3)
        glLineWidth(1.0)
        
        glBegin(GL_LINES)
        grid_size = 10
        grid_spacing = 1.0
        
        for i in range(-grid_size, grid_size + 1):
            # X-direction lines
            glVertex3f(i * grid_spacing, -grid_size * grid_spacing, 0.0)
            glVertex3f(i * grid_spacing, grid_size * grid_spacing, 0.0)
            
            # Y-direction lines
            glVertex3f(-grid_size * grid_spacing, i * grid_spacing, 0.0)
            glVertex3f(grid_size * grid_spacing, i * grid_spacing, 0.0)
        
        glEnd()
        glEnable(GL_LIGHTING)
    
    def render_axes(self):
        """Render coordinate axes."""
        glDisable(GL_LIGHTING)
        glLineWidth(3.0)
        
        glBegin(GL_LINES)
        
        # X-axis (red)
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(2.0, 0.0, 0.0)
        
        # Y-axis (green)
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, 2.0, 0.0)
        
        # Z-axis (blue)
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, 2.0)
        
        glEnd()
        glEnable(GL_LIGHTING)
    
    def render_mesh(self):
        """Render the loaded mesh."""
        if self.mesh_data is None:
            return
        
        vertices = self.mesh_data['vertices']
        faces = self.mesh_data['faces']
        normals = self.mesh_data['normals']
        
        # Set material color based on view mode
        if self.view_mode == "solid":
            glEnable(GL_LIGHTING)
            glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
            glColor3f(*self.material_diffuse[:3])
        elif self.view_mode == "wireframe":
            glDisable(GL_LIGHTING)
            glPolygonMode(GL_FRONT_AND_BACK, GL_LINE)
            glColor3f(0.8, 0.8, 0.8)
            glLineWidth(1.0)
        elif self.view_mode == "x-ray":
            glEnable(GL_LIGHTING)
            glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
            glEnable(GL_BLEND)
            glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
            glColor4f(*self.material_diffuse[:3], 0.3)
        
        # Render triangles
        glBegin(GL_TRIANGLES)
        for face in faces:
            for vertex_idx in face:
                if vertex_idx < len(normals):
                    glNormal3fv(normals[vertex_idx])
                if vertex_idx < len(vertices):
                    glVertex3fv(vertices[vertex_idx])
        glEnd()
        
        # Reset states
        if self.view_mode == "x-ray":
            glDisable(GL_BLEND)
        glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
        glEnable(GL_LIGHTING)
    
    def set_view_mode(self, mode):
        """Set the view mode (solid, wireframe, x-ray)."""
        self.view_mode = mode.lower()
        self.render()
    
    def reset_view(self):
        """Reset camera to default position."""
        self.camera_distance = 5.0
        self.camera_rotation_x = 20.0
        self.camera_rotation_y = 45.0
        self.camera_target = [0.0, 0.0, 0.0]
        self.render()
    
    def fit_view(self):
        """Fit the view to show the entire mesh."""
        if self.mesh_data is None:
            return
        
        bounds = self.mesh_data['bounds']
        center = (bounds[0] + bounds[1]) / 2
        size = np.max(bounds[1] - bounds[0])
        
        self.camera_target = center.tolist()
        self.camera_distance = size * 2.0
        self.render()
    
    # Event handlers
    def on_mouse_press(self, event):
        """Handle mouse button press."""
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        self.mouse_dragging = True
        self.mouse_button = event.num
    
    def on_mouse_drag(self, event):
        """Handle mouse drag for camera control."""
        if not self.mouse_dragging:
            return
        
        dx = event.x - self.last_mouse_x
        dy = event.y - self.last_mouse_y
        
        if self.mouse_button == 1:  # Left button - rotate
            self.camera_rotation_y += dx * 0.5
            self.camera_rotation_x += dy * 0.5
            
            # Clamp X rotation
            self.camera_rotation_x = max(-90, min(90, self.camera_rotation_x))
            
        elif self.mouse_button == 2:  # Middle button - pan
            pan_speed = 0.01 * self.camera_distance
            self.camera_target[0] -= dx * pan_speed
            self.camera_target[1] += dy * pan_speed
            
        elif self.mouse_button == 3:  # Right button - zoom
            zoom_speed = 0.01
            self.camera_distance += dy * zoom_speed
            self.camera_distance = max(0.1, min(50.0, self.camera_distance))
        
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        
        self.render()
    
    def on_mouse_release(self, event):
        """Handle mouse button release."""
        self.mouse_dragging = False
        self.mouse_button = None
    
    def on_mouse_wheel(self, event):
        """Handle mouse wheel for zooming."""
        zoom_factor = 0.1
        if event.delta > 0:
            self.camera_distance -= zoom_factor
        else:
            self.camera_distance += zoom_factor
        
        self.camera_distance = max(0.1, min(50.0, self.camera_distance))
        self.render()
    
    def on_resize(self, event):
        """Handle widget resize."""
        if not OPENGL_AVAILABLE:
            return
        
        self.width = event.width
        self.height = event.height
        
        self.opengl_widget.tkMakeCurrent()
        self.setup_viewport()
        self.render()
