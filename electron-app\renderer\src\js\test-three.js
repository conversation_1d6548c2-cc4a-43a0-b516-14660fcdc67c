// Simple Three.js test to verify it's working
function testThreeJS() {
    console.log('🧪 Testing Three.js...');
    
    if (typeof THREE === 'undefined') {
        console.error('❌ THREE.js not loaded');
        return false;
    }
    
    console.log('✅ THREE.js loaded:', THREE.REVISION);
    
    // Test basic Three.js functionality
    try {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        
        console.log('✅ Scene and camera created');
        
        // Test if WebGL is available
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (gl) {
            console.log('✅ WebGL is available');
            
            // Test renderer creation
            const renderer = new THREE.WebGLRenderer({ canvas: canvas });
            console.log('✅ WebGL renderer created');
            
            return true;
        } else {
            console.warn('⚠️ WebGL not available, falling back to software rendering');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Three.js test failed:', error);
        return false;
    }
}

// Test when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(testThreeJS, 1000);
});
