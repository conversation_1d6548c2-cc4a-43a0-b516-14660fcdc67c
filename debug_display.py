#!/usr/bin/env python3
"""Debug script to identify the display issue."""

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
from kotak_generator import ShapeGenerator
import numpy as np

def test_display():
    """Test the 3D display functionality."""
    print("Testing 3D display...")
    
    # Generate a simple mesh
    mesh = ShapeGenerator.create_rounded_box(100, 60, 40, 3, 5)
    print(f"Mesh created: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
    
    # Check mesh structure
    print(f"Vertices shape: {mesh.vertices.shape}")
    print(f"Faces shape: {mesh.faces.shape}")
    print(f"Sample vertices:\n{mesh.vertices[:3]}")
    print(f"Sample faces:\n{mesh.faces[:3]}")
    
    # Try to create the display
    try:
        fig = plt.figure()
        ax = fig.add_subplot(111, projection='3d')
        
        vertices = mesh.vertices
        faces = mesh.faces
        
        print(f"Processing {len(faces)} faces...")
        
        # This is where the error likely occurs
        face_vertices = []
        for i, face in enumerate(faces):
            if i < 3:  # Debug first few faces
                print(f"Face {i}: {face}")
                face_verts = vertices[face]
                print(f"Face vertices shape: {face_verts.shape}")
                print(f"Face vertices:\n{face_verts}")
            face_vertices.append(vertices[face])
        
        print("Creating Poly3DCollection...")
        collection = Poly3DCollection(face_vertices, alpha=0.7, facecolor='lightblue', edgecolor='black')
        ax.add_collection3d(collection)
        
        print("✓ Display test successful!")
        
    except Exception as e:
        print(f"✗ Display test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_display()
