/* Layout Styles */

/* Main App Container */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: var(--bg-primary);
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--toolbar-height);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0 var(--spacing-md);
  flex-shrink: 0;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toolbar-left {
  flex: 1;
}

.toolbar-center {
  flex: 0 0 auto;
}

.toolbar-right {
  flex: 1;
  justify-content: flex-end;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-primary);
  margin: 0 var(--spacing-sm);
}

/* Main Content */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Left Panel */
.left-panel {
  width: var(--panel-width);
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-shrink: 0;
}

/* Center Panel - 3D Viewport */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--viewport-bg);
  position: relative;
  overflow: hidden;
}

.viewport-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0 var(--spacing-md);
  flex-shrink: 0;
}

.viewport-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.viewport-canvas {
  width: 100%;
  height: 100%;
  display: block;
  background-color: var(--viewport-bg);
}

.viewport-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

/* Right Panel */
.right-panel {
  width: var(--panel-width);
  background-color: var(--bg-secondary);
  border-left: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex-shrink: 0;
}

/* Panel Sections */
.panel-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Tabs */
.panel-tabs {
  display: flex;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  flex-shrink: 0;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  height: 40px;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
}

.tab-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.tab-btn.active {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-bottom-color: var(--accent-primary);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  display: none;
}

.tab-content.active {
  display: block;
}

/* Property Groups */
.property-group {
  margin-bottom: var(--spacing-lg);
}

.property-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-primary);
}

.property-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.property-row:last-child {
  margin-bottom: 0;
}

.property-row label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  flex: 1;
}

.property-row input,
.property-row select {
  width: 80px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .toolbar-section span {
    display: none;
  }
  
  .left-panel,
  .right-panel {
    width: 200px;
  }
  
  .panel-title span {
    display: none;
  }
}

@media (max-width: 600px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
    border: none;
    border-top: 1px solid var(--border-primary);
  }
  
  .center-panel {
    order: -1;
  }
}
