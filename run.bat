@echo off
REM Windows batch file for Bambu Studio Style 3D Container Generator

echo.
echo 🚀 Starting Bambu Studio Style 3D Container Generator
echo ==================================================
echo.

REM Check if we're in the right directory
if not exist "README.md" (
    echo ❌ Error: Please run this script from the project root directory
    pause
    exit /b 1
)

REM Check for Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if backend is built
if not exist "backend\build\container_generator_server.exe" (
    echo ⚠️  Backend not found. The C++ backend needs to be built first.
    echo.
    echo For Windows, you can either:
    echo 1. Build with Visual Studio and CMake
    echo 2. Use the frontend-only mode
    echo.
    echo Starting frontend-only mode...
    goto :frontend_only
)

REM Start backend
echo 🔧 Starting C++ backend server...
cd backend\build
start "Backend Server" container_generator_server.exe --port 8081
cd ..\..

REM Wait for backend to start
timeout /t 3 /nobreak >nul

:frontend_only
REM Start frontend
echo 🌐 Starting frontend development server...
cd frontend

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start frontend server
echo.
echo ✅ Starting frontend server...
start "Frontend Server" cmd /k "npm start"

cd ..

echo.
echo ✅ Application started successfully!
echo.
echo 🌐 Frontend: http://localhost:8080
echo 🔧 Backend:  http://localhost:8081 (if available)
echo.
echo 📋 The application should open in your browser automatically.
echo    If not, navigate to: http://localhost:8080
echo.
echo 💡 To stop the servers, close the terminal windows that opened.
echo.
pause
