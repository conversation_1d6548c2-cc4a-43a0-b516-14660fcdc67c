#pragma once

#include <memory>
#include <string>
#include <thread>

// Forward declarations
class GeometryEngine;
class Renderer;
class Server;

/**
 * Main application class for the 3D Container Generator
 * Coordinates between geometry generation, OpenGL rendering, and web API
 */
class Application {
public:
    Application();
    ~Application();

    // Application lifecycle
    bool Initialize();
    void Run();
    void Shutdown();

    // Configuration
    struct Config {
        int serverPort = 8081;
        int windowWidth = 1024;
        int windowHeight = 768;
        bool headless = false;
        std::string logLevel = "INFO";
    };

    void SetConfig(const Config& config) { config_ = config; }
    const Config& GetConfig() const { return config_; }

private:
    // Core components
    std::unique_ptr<GeometryEngine> geometryEngine_;
    std::unique_ptr<Renderer> renderer_;
    std::unique_ptr<Server> server_;

    // Configuration
    Config config_;

    // Threading
    std::thread serverThread_;
    bool running_;

    // Initialization helpers
    bool InitializeGeometry();
    bool InitializeRenderer();
    bool InitializeServer();

    // Main loops
    void RenderLoop();
    void ServerLoop();
};

/**
 * Global application instance
 */
extern std::unique_ptr<Application> g_app;
