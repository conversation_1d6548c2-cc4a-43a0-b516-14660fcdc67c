#!/usr/bin/env python3
"""
Test script for the enhanced 3D container generator.
"""

from kotak_generator import <PERSON>hapeGenerator, simpan_stl
import os

def test_rectangular_container():
    """Test rectangular container generation."""
    print("Testing rectangular container...")
    try:
        mesh = ShapeGenerator.create_rounded_box(100, 60, 40, 3, corner_radius=5)
        print(f"✓ Rectangular container created: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
        
        # Test with individual corners
        mesh2 = ShapeGenerator.create_rounded_box(100, 60, 40, 3, 0, (2, 4, 6, 8))
        print(f"✓ Rectangular container with individual corners: {len(mesh2.vertices)} vertices, {len(mesh2.faces)} faces")
        
        return mesh
    except Exception as e:
        print(f"✗ Rectangular container test failed: {e}")
        return None

def test_triangular_container():
    """Test triangular container generation."""
    print("Testing triangular container...")
    try:
        mesh = ShapeGenerator.create_triangular_container(80, 40, 3, corner_radius=2)
        print(f"✓ Triangular container created: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
        return mesh
    except Exception as e:
        print(f"✗ Triangular container test failed: {e}")
        return None

def test_circular_container():
    """Test circular container generation."""
    print("Testing circular container...")
    try:
        mesh = ShapeGenerator.create_circular_container(80, 40, 3, segments=32)
        print(f"✓ Circular container created: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
        return mesh
    except Exception as e:
        print(f"✗ Circular container test failed: {e}")
        return None

def test_stl_export(mesh, filename):
    """Test STL export functionality."""
    print(f"Testing STL export to {filename}...")
    try:
        simpan_stl(mesh, filename)
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✓ STL file exported successfully: {filename} ({size} bytes)")
            return True
        else:
            print(f"✗ STL file not found: {filename}")
            return False
    except Exception as e:
        print(f"✗ STL export failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("Enhanced 3D Container Generator - Test Suite")
    print("=" * 50)
    
    # Test rectangular container
    rect_mesh = test_rectangular_container()
    if rect_mesh:
        test_stl_export(rect_mesh, "test_rectangular.stl")
    
    print()
    
    # Test triangular container
    tri_mesh = test_triangular_container()
    if tri_mesh:
        test_stl_export(tri_mesh, "test_triangular.stl")
    
    print()
    
    # Test circular container
    circ_mesh = test_circular_container()
    if circ_mesh:
        test_stl_export(circ_mesh, "test_circular.stl")
    
    print()
    print("=" * 50)
    print("Test suite completed!")
    print("=" * 50)

if __name__ == "__main__":
    main()
