#pragma once

#include <vector>
#include <memory>
#include <string>

// 3D Vector structure
struct Vec3 {
    float x, y, z;
    Vec3(float x = 0, float y = 0, float z = 0) : x(x), y(y), z(z) {}
    Vec3 operator+(const Vec3& other) const { return Vec3(x + other.x, y + other.y, z + other.z); }
    Vec3 operator-(const Vec3& other) const { return Vec3(x - other.x, y - other.y, z - other.z); }
    Vec3 operator*(float scalar) const { return Vec3(x * scalar, y * scalar, z * scalar); }
};

// Triangle face structure
struct Triangle {
    int v1, v2, v3;  // Vertex indices
    Triangle(int v1, int v2, int v3) : v1(v1), v2(v2), v3(v3) {}
};

// 3D Mesh structure
struct Mesh {
    std::vector<Vec3> vertices;
    std::vector<Triangle> faces;
    std::vector<Vec3> normals;
    
    void Clear() {
        vertices.clear();
        faces.clear();
        normals.clear();
    }
    
    void CalculateNormals();
    void Scale(float factor);
    void Translate(const Vec3& offset);
    bool IsValid() const;
};

// Container parameters
struct ContainerParams {
    enum ShapeType { BOX, CYLINDER, PRISM };
    enum EdgeType { SHARP, ROUNDED, CHAMFERED };
    
    ShapeType shapeType = BOX;
    
    // Dimensions
    float length = 100.0f;
    float width = 60.0f;
    float height = 40.0f;
    float wallThickness = 3.0f;
    
    // Edge modification
    EdgeType edgeType = SHARP;
    float edgeSize = 0.0f;
    
    // Quality settings
    int segments = 32;  // For cylinders and prisms
    int sides = 6;      // For prisms
    
    // Validation
    bool IsValid() const;
    std::string GetValidationError() const;
};

/**
 * Advanced geometry engine for generating 3D container meshes
 */
class GeometryEngine {
public:
    GeometryEngine();
    ~GeometryEngine();

    // Main generation function
    std::unique_ptr<Mesh> GenerateContainer(const ContainerParams& params);

    // Shape-specific generators
    std::unique_ptr<Mesh> GenerateBox(const ContainerParams& params);
    std::unique_ptr<Mesh> GenerateCylinder(const ContainerParams& params);
    std::unique_ptr<Mesh> GeneratePrism(const ContainerParams& params);

    // Edge processing
    void ApplyEdgeModification(Mesh& mesh, const ContainerParams& params);
    void ApplyRounding(Mesh& mesh, float radius, int segments = 8);
    void ApplyChamfering(Mesh& mesh, float distance);

    // Utility functions
    std::unique_ptr<Mesh> CreateHollowMesh(const Mesh& outer, const Mesh& inner);
    bool ExportSTL(const Mesh& mesh, const std::string& filename);
    
    // Statistics
    struct Stats {
        int totalVertices = 0;
        int totalFaces = 0;
        float generationTime = 0.0f;
        std::string lastError;
    };
    
    const Stats& GetStats() const { return stats_; }

private:
    Stats stats_;
    
    // Internal mesh operations
    void MergeMeshes(Mesh& target, const Mesh& source);
    void OptimizeMesh(Mesh& mesh);
    void ValidateMesh(const Mesh& mesh);
    
    // Boolean operations
    std::unique_ptr<Mesh> SubtractMeshes(const Mesh& a, const Mesh& b);
    std::unique_ptr<Mesh> UnionMeshes(const Mesh& a, const Mesh& b);
    
    // Edge detection and processing
    std::vector<std::pair<int, int>> FindEdges(const Mesh& mesh);
    void SmoothEdges(Mesh& mesh, const std::vector<std::pair<int, int>>& edges, float factor);
};
