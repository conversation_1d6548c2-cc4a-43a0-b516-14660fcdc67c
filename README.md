# 🎯 3D Container Generator - Bambu Studio Style

A professional 3D container modeling application inspired by Bambu Lab Studio, featuring OpenGL-powered 3D preview and advanced geometry generation capabilities.

## ✨ Features

### 🎨 **Professional Interface**
- **Dark Theme**: Modern Bambu Lab Studio inspired interface
- **Organized Layout**: Toolbar, tool panels, 3D viewport, and properties panel
- **Intuitive Controls**: Professional workflow with familiar UI patterns

### 🔧 **Shape Generation**
- **📦 Box Containers**: Rectangular containers with customizable dimensions
- **🥫 Cylinder Containers**: Circular containers with adjustable quality
- **🔺 Prism Containers**: Multi-sided containers (3+ sides)

### ⚙️ **Edge Modification**
- **🔄 Round Edges**: Smooth rounded corners and edges
- **✂️ Chamfer Edges**: Angled edge cuts for modern aesthetics
- **↩️ Reset Edges**: Return to sharp edges

### 🖥️ **3D Viewport**
- **OpenGL Rendering**: Hardware-accelerated 3D preview (when available)
- **Fallback Support**: Works even without OpenGL
- **View Modes**: Solid, Wireframe, X-Ray visualization
- **Interactive Controls**: Mouse-based camera control (rotate, pan, zoom)
- **Reference Grid**: Visual grid and coordinate axes

### 📁 **Export & Import**
- **STL Export**: Direct export for 3D printing
- **Quality Settings**: Low, Medium, High, Ultra quality options
- **Unit Support**: mm, cm, inches
- **Material Properties**: PLA, PETG, ABS, TPU presets

## 🚀 Installation

### Prerequisites
```bash
pip install -r requirements.txt
```

### Required Dependencies
- `trimesh>=3.15.0` - 3D mesh processing
- `numpy>=1.21.0` - Numerical computations
- `PyOpenGL>=3.1.0` - OpenGL rendering (optional)
- `PyOpenGL-accelerate>=3.1.0` - OpenGL acceleration (optional)
- `Pillow>=9.0.0` - Image processing
- `moderngl>=5.6.0` - Modern OpenGL (optional)

## 🎮 Usage

### Launch Application
```bash
python bambu_studio.py
```

### Interface Overview

#### **Left Panel - Shape Tools**
- **Shape Selection**: Choose between Box, Cylinder, or Prism
- **Edge Tools**: Apply rounding, chamfering, or reset edges

#### **Center Panel - 3D Viewport**
- **View Modes**: Switch between Solid, Wireframe, X-Ray
- **Mouse Controls**:
  - **Left Click + Drag**: Rotate camera
  - **Middle Click + Drag**: Pan view
  - **Right Click + Drag**: Zoom
  - **Mouse Wheel**: Zoom in/out

#### **Right Panel - Properties**
- **Geometry Tab**: Dimensions, wall thickness, edge modification
- **Material Tab**: Material type and color selection
- **Export Tab**: Export settings and STL generation

### Creating Containers

1. **Select Shape**: Choose Box, Cylinder, or Prism from the left panel
2. **Set Dimensions**: Enter length, width, height in the Geometry tab
3. **Configure Walls**: Set wall thickness for hollow containers
4. **Modify Edges**: Choose Sharp, Rounded, or Chamfered edges
5. **Preview**: View your design in the 3D viewport
6. **Export**: Save as STL file for 3D printing

### Edge Modification

#### **Rounded Edges**
- Select "Rounded" from Edge Type dropdown
- Set radius value (recommended: 1-5mm)
- Preview updates automatically

#### **Chamfered Edges**
- Select "Chamfered" from Edge Type dropdown
- Set chamfer distance (recommended: 1-3mm)
- Creates angled cuts on edges

#### **Sharp Edges**
- Select "Sharp" for clean, angular edges
- Default setting for precise containers

## 🎛️ Controls Reference

### **Toolbar**
- **New**: Create new project
- **Open**: Load project (future feature)
- **Save**: Save project (future feature)
- **Export STL**: Export current model
- **Reset View**: Return camera to default position
- **Fit View**: Frame model in viewport

### **3D Viewport Controls**
- **Rotate**: Left mouse button + drag
- **Pan**: Middle mouse button + drag (or Shift + left drag)
- **Zoom**: Right mouse button + drag or mouse wheel
- **View Modes**: Solid, Wireframe, X-Ray

### **Keyboard Shortcuts** (Future)
- `Ctrl+N`: New project
- `Ctrl+O`: Open project
- `Ctrl+S`: Save project
- `Ctrl+E`: Export STL
- `R`: Reset view
- `F`: Fit view

## 🔧 Technical Details

### **Architecture**
- **bambu_studio.py**: Main application with UI
- **geometry_engine.py**: 3D shape generation engine
- **opengl_viewer.py**: OpenGL 3D viewport component

### **Geometry Engine**
- **Advanced Shape Creation**: Precise hollow container generation
- **Quality Settings**: Adjustable mesh resolution
- **Edge Processing**: Rounding and chamfering algorithms
- **Error Handling**: Graceful fallbacks for complex operations

### **OpenGL Viewer**
- **Hardware Acceleration**: Uses GPU when available
- **Fallback Mode**: Text-based info when OpenGL unavailable
- **Professional Lighting**: Realistic material rendering
- **Smooth Interaction**: 60fps camera controls

## 🎯 Use Cases

### **3D Printing**
- **Storage Containers**: Custom organizers for tools, parts, crafts
- **Desk Accessories**: Pen holders, cable management, organizers
- **Workshop Storage**: Bins, trays, compartments
- **Hobby Projects**: Game piece holders, miniature storage

### **Prototyping**
- **Design Validation**: Quick container mockups
- **Fit Testing**: Check dimensions before final production
- **Concept Development**: Iterate on container designs

### **Education**
- **3D Modeling**: Learn container design principles
- **Manufacturing**: Understand wall thickness and tolerances
- **CAD Workflow**: Professional modeling techniques

## 🐛 Troubleshooting

### **OpenGL Issues**
- **No 3D Preview**: Install PyOpenGL packages
- **Slow Performance**: Update graphics drivers
- **Fallback Mode**: Application works without OpenGL

### **Geometry Issues**
- **Boolean Operation Failed**: Uses solid shapes as fallback
- **Invalid Parameters**: Check dimension and thickness values
- **Export Errors**: Ensure valid mesh before exporting

### **Performance Tips**
- **Lower Quality**: Use "Medium" or "Low" for faster preview
- **Reduce Segments**: Lower cylinder/prism segment count
- **Simple Edges**: Use "Sharp" edges for better performance

## 🔮 Future Enhancements

- **Project Save/Load**: Store and retrieve designs
- **Advanced Materials**: More material presets and properties
- **Texture Support**: Surface textures and patterns
- **Assembly Mode**: Multi-part container systems
- **Parametric Design**: Constraint-based modeling
- **Plugin System**: Extensible tool architecture

## 📄 License

Open source project - feel free to modify and distribute.

## 🤝 Contributing

Contributions welcome! Areas for improvement:
- Advanced edge processing algorithms
- Additional shape primitives
- Enhanced OpenGL rendering
- UI/UX improvements
- Performance optimizations

---

**🎉 Ready to create amazing 3D containers with professional tools!**

Launch with: `python bambu_studio.py`
