# Enhanced 3D Container Generator

A comprehensive Python application for generating 3D printable containers with multiple base shapes, corner rounding, and integrated preview functionality.

## Features

### 🔧 **Multiple Container Shapes**
- **Rectangular Containers**: Traditional box-shaped containers with customizable dimensions
- **Triangular Containers**: Triangular base containers for unique storage needs
- **Circular Containers**: Cylindrical containers with smooth curves

### 🎨 **Advanced Corner Rounding**
- **Synchronized Rounding**: Apply the same radius to all corners simultaneously
- **Individual Corner Control**: Set different radius values for each corner independently
- **Geometry Preservation**: Maintains structural integrity while rounding corners

### 🖥️ **Integrated User Interface**
- **Single Window Design**: No more separate preview windows - everything in one place
- **Real-time 3D Preview**: See your design update as you modify parameters
- **Tabbed Interface**: Easy switching between different container shapes
- **Modern UI**: Clean, intuitive interface built with tkinter and ttk

### ⚙️ **Smart Parameter Validation**
- **Real-time Validation**: Instant feedback on parameter validity
- **Intelligent Limits**: Automatic calculation of maximum wall thickness and corner radii
- **Error Prevention**: Prevents invalid geometries before generation

### 📁 **Export Capabilities**
- **STL Export**: Direct export to STL format for 3D printing
- **High-Quality Meshes**: Optimized mesh generation for clean prints

## Installation

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Required Packages
- `trimesh>=3.15.0` - 3D mesh processing
- `matplotlib>=3.5.0` - 3D visualization
- `numpy>=1.21.0` - Numerical computations
- `vedo>=2023.4.0` - Advanced 3D visualization (for legacy support)

## Usage

### Running the Enhanced Application
```bash
python enhanced_app.py
```

### Running the Legacy Application
```bash
python app_gui.py
```

### Command Line Testing
```bash
python test_enhanced.py
```

## Application Guide

### 1. **Rectangular Containers**
- Set **Length**, **Width**, **Height** in millimeters
- Adjust **Wall Thickness** (must be less than half the smallest dimension)
- Configure corner rounding:
  - Check "Sync all corners" for uniform rounding
  - Uncheck to set individual corner radii

### 2. **Triangular Containers**
- Set **Side Length** for equilateral triangle base
- Adjust **Height** and **Wall Thickness**
- Optional **Corner Radius** for rounded edges

### 3. **Circular Containers**
- Set **Diameter** and **Height**
- Adjust **Wall Thickness** (must be less than radius)
- Configure **Segments** for mesh quality (higher = smoother)

### 4. **Export Process**
1. Design your container using the parameter controls
2. Verify the design in the 3D preview
3. Click "Export STL" to save your model
4. Choose filename and location
5. Import the STL file into your 3D printing software

## Technical Architecture

### Core Components

#### `ShapeGenerator` Class
- **`create_rounded_box()`**: Generates rectangular containers with optional corner rounding
- **`create_triangular_container()`**: Creates triangular base containers
- **`create_circular_container()`**: Produces cylindrical containers
- **`validate_parameters()`**: Ensures parameter validity across all shapes

#### `Enhanced3DApp` Class
- **Integrated UI**: Single-window interface with tabbed shape selection
- **Real-time Preview**: Matplotlib-based 3D visualization
- **Parameter Management**: Automatic validation and synchronization
- **Export Functionality**: Direct STL file generation

### File Structure
```
├── enhanced_app.py          # Main enhanced application
├── kotak_generator.py       # Core shape generation engine
├── app_gui.py              # Legacy application (backward compatibility)
├── preview_vedo.py         # Legacy preview script
├── test_enhanced.py        # Comprehensive test suite
├── test_backend.py         # Legacy backend test
├── requirements.txt        # Python dependencies
└── README.md              # This documentation
```

## Advanced Features

### Corner Rounding System
The corner rounding system preserves the overall geometry while smoothing edges:
- **Automatic Limits**: Calculates maximum possible radius based on dimensions
- **Geometry Validation**: Prevents invalid configurations
- **Individual Control**: Each corner can have different radius values

### Real-time Preview
- **Immediate Feedback**: Changes appear instantly in the 3D view
- **Interactive Visualization**: Rotate, zoom, and pan the 3D model
- **Performance Optimized**: Efficient mesh generation and display

### Parameter Synchronization
- **Debounced Updates**: Prevents excessive recalculation during typing
- **Smart Validation**: Real-time parameter checking with helpful error messages
- **Automatic Correction**: Suggests valid ranges when parameters are out of bounds

## Best Practices

### For 3D Printing
1. **Wall Thickness**: Use at least 1.2mm for FDM printers, 0.8mm for resin printers
2. **Corner Radius**: Small radii (1-3mm) often print better than sharp corners
3. **Overhangs**: Circular containers may need supports depending on height

### For Design
1. **Start Simple**: Begin with basic shapes before adding complex features
2. **Test Parameters**: Use the preview to verify your design before exporting
3. **Consider Use Case**: Match container shape to intended contents

## Troubleshooting

### Common Issues
- **"Ketebalan terlalu besar"**: Reduce wall thickness
- **"Corner radius terlalu besar"**: Reduce corner radius values
- **Preview not updating**: Check parameter values for validity
- **Export fails**: Ensure a valid mesh is generated in preview first

### Performance Tips
- **Circular Containers**: Reduce segment count for faster preview (increase for final export)
- **Large Models**: Consider reducing preview quality for real-time interaction
- **Memory Usage**: Close and reopen application for very large models

## Contributing

This project uses modern Python best practices:
- Type hints for better code documentation
- Modular design for easy extension
- Comprehensive error handling
- Extensive testing coverage

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Version History

- **v2.0**: Enhanced application with integrated preview and multiple shapes
- **v1.0**: Original rectangular container generator

---

**Happy 3D Printing!** 🎯
