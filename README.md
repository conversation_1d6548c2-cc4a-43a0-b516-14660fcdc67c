# 🎯 Bambu Studio Style 3D Container Generator

A professional 3D container modeling application inspired by Bambu Lab Studio, featuring:

- **C++ Backend**: High-performance geometry generation with OpenGL
- **Three.js Frontend**: Modern web-based 3D visualization
- **Professional UI**: Bambu Lab Studio inspired interface

## 🏗️ Architecture

```
3D Container Generator/
├── backend/                 # C++ Backend
│   ├── src/
│   │   ├── geometry/       # 3D geometry generation
│   │   ├── opengl/         # OpenGL rendering
│   │   └── api/            # REST API server
│   ├── include/            # Header files
│   ├── CMakeLists.txt      # Build configuration
│   └── build/              # Build output
├── frontend/               # Web Frontend
│   ├── src/
│   │   ├── js/             # JavaScript modules
│   │   ├── css/            # Stylesheets
│   │   └── components/     # UI components
│   ├── assets/             # Static assets
│   ├── index.html          # Main application
│   └── package.json        # Dependencies
├── shared/                 # Shared resources
│   ├── models/             # 3D model definitions
│   └── protocols/          # API protocols
└── docs/                   # Documentation
```

## ✨ Features

### 🎨 **Professional Interface**

- **Bambu Studio Theme**: Dark, modern UI matching Bambu Lab Studio
- **Responsive Layout**: Toolbar, panels, 3D viewport
- **Real-time Preview**: Instant visual feedback

### 🔧 **Shape Generation**

- **📦 Box Containers**: Rectangular with customizable dimensions
- **🥫 Cylinder Containers**: Circular with quality settings
- **🔺 Prism Containers**: Multi-sided (3+ sides)
- **Hollow Design**: All containers have internal cavities

### ⚙️ **Edge Manipulation**

- **🔄 Round Edges**: Smooth corner rounding
- **✂️ Chamfer Edges**: Angled edge cuts
- **🎛️ Parametric Control**: Precise edge modification

### 🖥️ **3D Visualization**

- **Three.js Rendering**: Hardware-accelerated WebGL
- **Interactive Controls**: Orbit, pan, zoom
- **Multiple Views**: Solid, wireframe, x-ray
- **Real-time Updates**: Immediate geometry changes

### 📁 **Export & Import**

- **STL Export**: Direct download for 3D printing
- **High Quality**: Configurable mesh resolution
- **Batch Export**: Multiple formats support

## 🚀 Quick Start

### Prerequisites

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install cmake g++ libglfw3-dev libglew-dev libassimp-dev nodejs npm

# macOS
brew install cmake glfw glew assimp node

# Windows (using vcpkg)
vcpkg install glfw3 glew assimp
```

### Build & Run

```bash
# Automated build (recommended)
./build.sh

# Start application
./run.sh

# Manual build (alternative)
cd backend && mkdir build && cd build
cmake .. && make
cd ../../frontend && npm install
```

### Access Application

- **Frontend**: http://localhost:8080
- **Backend API**: http://localhost:8081

## 🎮 Usage

1. **Open Application**: Navigate to http://localhost:8080
2. **Select Shape**: Choose Box, Cylinder, or Prism
3. **Set Parameters**: Adjust dimensions and wall thickness
4. **Modify Edges**: Apply rounding or chamfering
5. **Preview**: View in real-time 3D
6. **Export**: Download STL for 3D printing

## 🔧 Development

### Backend (C++)

- **Geometry Engine**: Advanced 3D mesh generation
- **OpenGL Renderer**: Hardware-accelerated preview
- **REST API**: JSON communication with frontend
- **STL Exporter**: High-quality mesh export

### Frontend (JavaScript/Three.js)

- **Three.js Scene**: Professional 3D visualization
- **Modern UI**: CSS Grid/Flexbox layout
- **API Client**: Async communication with backend
- **File Management**: Download and upload handling

## 📋 API Endpoints

```
POST /api/generate     # Generate 3D geometry
GET  /api/preview      # Get preview mesh
POST /api/export       # Export STL file
GET  /api/status       # Server status
```

## 🎯 Roadmap

- [x] Project structure
- [ ] C++ geometry engine
- [ ] OpenGL renderer
- [ ] REST API server
- [ ] Three.js frontend
- [ ] Bambu Studio UI theme
- [ ] Edge manipulation
- [ ] STL export
- [ ] Testing & optimization

---

**🎉 Professional 3D container generation with modern web technology!**
