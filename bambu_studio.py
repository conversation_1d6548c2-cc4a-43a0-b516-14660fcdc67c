#!/usr/bin/env python3
"""
3D Container Generator - Bambu Lab Studio Style
Professional 3D modeling application for creating containers with OpenGL preview.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import numpy as np
import threading
import os
import sys

# OpenGL imports
try:
    from OpenGL.GL import *
    from OpenGL.GLU import *
    import tkinter.opengl as togl
    OPENGL_AVAILABLE = True
except ImportError:
    try:
        # Alternative OpenGL setup
        import moderngl
        import moderngl_window as mglw
        MODERNGL_AVAILABLE = True
        OPENGL_AVAILABLE = False
    except ImportError:
        OPENGL_AVAILABLE = False
        MODERNGL_AVAILABLE = False

from geometry_engine import GeometryEngine
from opengl_viewer import OpenGLViewer

class BambuStudioApp:
    """Main application class inspired by Bambu Lab Studio interface."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("3D Container Generator - Bambu Studio Style")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#2b2b2b')  # Dark theme like Bambu Studio
        
        # Application state
        self.current_mesh = None
        self.current_shape = "box"
        self.geometry_engine = GeometryEngine()
        
        # Setup UI
        self.setup_styles()
        self.setup_ui()
        self.setup_default_values()
        
        # Generate initial preview
        self.update_preview()
    
    def setup_styles(self):
        """Setup modern dark theme styles."""
        style = ttk.Style()
        
        # Configure dark theme
        style.theme_use('clam')
        
        # Dark theme colors
        bg_color = '#2b2b2b'
        fg_color = '#ffffff'
        select_color = '#0078d4'
        
        style.configure('Dark.TFrame', background=bg_color)
        style.configure('Dark.TLabel', background=bg_color, foreground=fg_color)
        style.configure('Dark.TButton', background='#404040', foreground=fg_color)
        style.configure('Dark.TEntry', background='#404040', foreground=fg_color)
        style.configure('Dark.TCombobox', background='#404040', foreground=fg_color)
        style.configure('Dark.TNotebook', background=bg_color)
        style.configure('Dark.TNotebook.Tab', background='#404040', foreground=fg_color)
        
        # Map styles for different states
        style.map('Dark.TButton',
                 background=[('active', select_color), ('pressed', '#005a9e')])
        style.map('Dark.TEntry',
                 background=[('focus', '#505050')])
    
    def setup_ui(self):
        """Setup the main user interface."""
        # Main container
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Top toolbar
        self.setup_toolbar(main_frame)
        
        # Main content area
        content_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # Left panel (tools and properties)
        self.setup_left_panel(content_frame)
        
        # Center panel (3D viewport)
        self.setup_center_panel(content_frame)
        
        # Right panel (parameters and settings)
        self.setup_right_panel(content_frame)
    
    def setup_toolbar(self, parent):
        """Setup the top toolbar."""
        toolbar = ttk.Frame(parent, style='Dark.TFrame')
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # File operations
        ttk.Button(toolbar, text="New", style='Dark.TButton', 
                  command=self.new_project).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Open", style='Dark.TButton', 
                  command=self.open_project).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Save", style='Dark.TButton', 
                  command=self.save_project).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Export
        ttk.Button(toolbar, text="Export STL", style='Dark.TButton', 
                  command=self.export_stl).pack(side=tk.LEFT, padx=2)
        
        # View controls
        ttk.Separator(toolbar, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)
        ttk.Button(toolbar, text="Reset View", style='Dark.TButton', 
                  command=self.reset_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Fit View", style='Dark.TButton', 
                  command=self.fit_view).pack(side=tk.LEFT, padx=2)
        
        # Status
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(toolbar, textvariable=self.status_var, style='Dark.TLabel')
        status_label.pack(side=tk.RIGHT, padx=10)
    
    def setup_left_panel(self, parent):
        """Setup the left tool panel."""
        left_panel = ttk.Frame(parent, style='Dark.TFrame', width=250)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_panel.pack_propagate(False)
        
        # Shape selection
        shape_frame = ttk.LabelFrame(left_panel, text="Shape Tools", style='Dark.TFrame')
        shape_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Shape buttons with icons (text for now)
        shapes = [
            ("Box", "box", "📦"),
            ("Cylinder", "cylinder", "🥫"),
            ("Prism", "prism", "🔺")
        ]
        
        self.shape_var = tk.StringVar(value="box")
        
        for i, (name, value, icon) in enumerate(shapes):
            btn = ttk.Radiobutton(shape_frame, text=f"{icon} {name}", 
                                 variable=self.shape_var, value=value,
                                 style='Dark.TButton', command=self.on_shape_change)
            btn.pack(fill=tk.X, padx=5, pady=2)
        
        # Tools
        tools_frame = ttk.LabelFrame(left_panel, text="Edge Tools", style='Dark.TFrame')
        tools_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(tools_frame, text="🔄 Round Edges", style='Dark.TButton',
                  command=self.apply_rounding).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(tools_frame, text="✂️ Chamfer Edges", style='Dark.TButton',
                  command=self.apply_chamfer).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(tools_frame, text="↩️ Reset Edges", style='Dark.TButton',
                  command=self.reset_edges).pack(fill=tk.X, padx=5, pady=2)
    
    def setup_center_panel(self, parent):
        """Setup the center 3D viewport."""
        center_panel = ttk.Frame(parent, style='Dark.TFrame')
        center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # Viewport header
        viewport_header = ttk.Frame(center_panel, style='Dark.TFrame')
        viewport_header.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(viewport_header, text="3D Viewport", 
                 style='Dark.TLabel', font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        
        # View mode selector
        view_frame = ttk.Frame(viewport_header, style='Dark.TFrame')
        view_frame.pack(side=tk.RIGHT)
        
        ttk.Label(view_frame, text="View:", style='Dark.TLabel').pack(side=tk.LEFT, padx=5)
        self.view_mode = ttk.Combobox(view_frame, values=["Solid", "Wireframe", "X-Ray"], 
                                     state="readonly", style='Dark.TCombobox', width=10)
        self.view_mode.set("Solid")
        self.view_mode.pack(side=tk.LEFT)
        self.view_mode.bind('<<ComboboxSelected>>', self.on_view_mode_change)
        
        # 3D Viewer
        self.viewer = OpenGLViewer(center_panel, width=800, height=600)
        self.viewer.pack(fill=tk.BOTH, expand=True)
    
    def setup_right_panel(self, parent):
        """Setup the right properties panel."""
        right_panel = ttk.Frame(parent, style='Dark.TFrame', width=300)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_panel.pack_propagate(False)
        
        # Properties notebook
        self.properties_notebook = ttk.Notebook(right_panel, style='Dark.TNotebook')
        self.properties_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Geometry tab
        self.setup_geometry_tab()
        
        # Material tab
        self.setup_material_tab()
        
        # Export tab
        self.setup_export_tab()
    
    def setup_geometry_tab(self):
        """Setup geometry properties tab."""
        geom_frame = ttk.Frame(self.properties_notebook, style='Dark.TFrame')
        self.properties_notebook.add(geom_frame, text="Geometry")
        
        # Dimensions
        dims_frame = ttk.LabelFrame(geom_frame, text="Dimensions (mm)", style='Dark.TFrame')
        dims_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Length/Diameter
        ttk.Label(dims_frame, text="Length/Diameter:", style='Dark.TLabel').grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.length_var = tk.StringVar(value="100")
        self.length_entry = ttk.Entry(dims_frame, textvariable=self.length_var, style='Dark.TEntry', width=12)
        self.length_entry.grid(row=0, column=1, padx=5, pady=2)
        self.length_var.trace('w', self.on_param_change)
        
        # Width
        ttk.Label(dims_frame, text="Width:", style='Dark.TLabel').grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.width_var = tk.StringVar(value="60")
        self.width_entry = ttk.Entry(dims_frame, textvariable=self.width_var, style='Dark.TEntry', width=12)
        self.width_entry.grid(row=1, column=1, padx=5, pady=2)
        self.width_var.trace('w', self.on_param_change)
        
        # Height
        ttk.Label(dims_frame, text="Height:", style='Dark.TLabel').grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.height_var = tk.StringVar(value="40")
        self.height_entry = ttk.Entry(dims_frame, textvariable=self.height_var, style='Dark.TEntry', width=12)
        self.height_entry.grid(row=2, column=1, padx=5, pady=2)
        self.height_var.trace('w', self.on_param_change)
        
        # Wall thickness
        ttk.Label(dims_frame, text="Wall Thickness:", style='Dark.TLabel').grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.thickness_var = tk.StringVar(value="3")
        self.thickness_entry = ttk.Entry(dims_frame, textvariable=self.thickness_var, style='Dark.TEntry', width=12)
        self.thickness_entry.grid(row=3, column=1, padx=5, pady=2)
        self.thickness_var.trace('w', self.on_param_change)
        
        # Edge modification
        edge_frame = ttk.LabelFrame(geom_frame, text="Edge Modification", style='Dark.TFrame')
        edge_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Edge type
        ttk.Label(edge_frame, text="Edge Type:", style='Dark.TLabel').grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.edge_type = ttk.Combobox(edge_frame, values=["Sharp", "Rounded", "Chamfered"], 
                                     state="readonly", style='Dark.TCombobox', width=10)
        self.edge_type.set("Sharp")
        self.edge_type.grid(row=0, column=1, padx=5, pady=2)
        self.edge_type.bind('<<ComboboxSelected>>', self.on_edge_type_change)
        
        # Edge radius/size
        ttk.Label(edge_frame, text="Radius/Size:", style='Dark.TLabel').grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.edge_size_var = tk.StringVar(value="0")
        self.edge_size_entry = ttk.Entry(edge_frame, textvariable=self.edge_size_var, style='Dark.TEntry', width=12)
        self.edge_size_entry.grid(row=1, column=1, padx=5, pady=2)
        self.edge_size_var.trace('w', self.on_param_change)
    
    def setup_material_tab(self):
        """Setup material properties tab."""
        material_frame = ttk.Frame(self.properties_notebook, style='Dark.TFrame')
        self.properties_notebook.add(material_frame, text="Material")
        
        # Material selection
        mat_frame = ttk.LabelFrame(material_frame, text="Material Properties", style='Dark.TFrame')
        mat_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(mat_frame, text="Material:", style='Dark.TLabel').grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.material_var = ttk.Combobox(mat_frame, values=["PLA", "PETG", "ABS", "TPU"], 
                                        state="readonly", style='Dark.TCombobox', width=12)
        self.material_var.set("PLA")
        self.material_var.grid(row=0, column=1, padx=5, pady=2)
        
        # Color
        ttk.Label(mat_frame, text="Color:", style='Dark.TLabel').grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.color_var = ttk.Combobox(mat_frame, values=["Blue", "Red", "Green", "White", "Black"], 
                                     state="readonly", style='Dark.TCombobox', width=12)
        self.color_var.set("Blue")
        self.color_var.grid(row=1, column=1, padx=5, pady=2)
    
    def setup_export_tab(self):
        """Setup export properties tab."""
        export_frame = ttk.Frame(self.properties_notebook, style='Dark.TFrame')
        self.properties_notebook.add(export_frame, text="Export")
        
        # Export settings
        export_settings = ttk.LabelFrame(export_frame, text="Export Settings", style='Dark.TFrame')
        export_settings.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(export_settings, text="Units:", style='Dark.TLabel').grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.units_var = ttk.Combobox(export_settings, values=["mm", "cm", "inches"], 
                                     state="readonly", style='Dark.TCombobox', width=12)
        self.units_var.set("mm")
        self.units_var.grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(export_settings, text="Quality:", style='Dark.TLabel').grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.quality_var = ttk.Combobox(export_settings, values=["Low", "Medium", "High", "Ultra"], 
                                       state="readonly", style='Dark.TCombobox', width=12)
        self.quality_var.set("High")
        self.quality_var.grid(row=1, column=1, padx=5, pady=2)
        
        # Export button
        ttk.Button(export_settings, text="Export STL", style='Dark.TButton',
                  command=self.export_stl).grid(row=2, column=0, columnspan=2, pady=10, sticky="ew")
    
    def setup_default_values(self):
        """Setup default values and initial state."""
        self.current_shape = "box"
        self.update_ui_for_shape()
    
    # Event handlers
    def on_shape_change(self):
        """Handle shape selection change."""
        self.current_shape = self.shape_var.get()
        self.update_ui_for_shape()
        self.update_preview()
    
    def on_param_change(self, *args):
        """Handle parameter change with debouncing."""
        if hasattr(self, '_update_timer'):
            self.root.after_cancel(self._update_timer)
        self._update_timer = self.root.after(300, self.update_preview)
    
    def on_view_mode_change(self, event=None):
        """Handle view mode change."""
        mode = self.view_mode.get()
        self.viewer.set_view_mode(mode)
    
    def on_edge_type_change(self, event=None):
        """Handle edge type change."""
        edge_type = self.edge_type.get()
        if edge_type == "Sharp":
            self.edge_size_var.set("0")
            self.edge_size_entry.config(state="disabled")
        else:
            self.edge_size_entry.config(state="normal")
        self.update_preview()
    
    def update_ui_for_shape(self):
        """Update UI elements based on selected shape."""
        shape = self.current_shape
        
        if shape == "cylinder":
            # For cylinder, width is not applicable
            self.width_entry.config(state="disabled")
        elif shape == "prism":
            # For prism, width represents number of sides or similar
            self.width_entry.config(state="normal")
        else:  # box
            self.width_entry.config(state="normal")
    
    def update_preview(self):
        """Update the 3D preview."""
        try:
            self.status_var.set("Generating geometry...")
            self.root.update()
            
            # Get parameters
            params = self.get_current_parameters()
            
            # Generate geometry in background thread
            def generate_geometry():
                try:
                    mesh = self.geometry_engine.create_shape(**params)
                    self.current_mesh = mesh
                    
                    # Update viewer in main thread
                    self.root.after(0, lambda: self.update_viewer(mesh))
                    
                except Exception as e:
                    error_msg = str(e)
                    self.root.after(0, lambda: self.show_error(error_msg))
            
            thread = threading.Thread(target=generate_geometry)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.show_error(str(e))
    
    def update_viewer(self, mesh):
        """Update the 3D viewer with new mesh."""
        try:
            self.viewer.load_mesh(mesh)
            face_count = len(mesh.faces) if hasattr(mesh, 'faces') else 0
            self.status_var.set(f"Ready - {face_count} faces")
        except Exception as e:
            self.show_error(f"Viewer update failed: {str(e)}")
    
    def get_current_parameters(self):
        """Get current parameters for geometry generation."""
        try:
            params = {
                'shape_type': self.current_shape,
                'length': float(self.length_var.get()),
                'width': float(self.width_var.get()) if self.current_shape != "cylinder" else None,
                'height': float(self.height_var.get()),
                'wall_thickness': float(self.thickness_var.get()),
                'edge_type': self.edge_type.get().lower(),
                'edge_size': float(self.edge_size_var.get()),
                'quality': self.quality_var.get().lower()
            }
            return params
        except ValueError as e:
            raise ValueError("Please enter valid numeric values for all parameters.")
    
    def show_error(self, message):
        """Show error message."""
        self.status_var.set(f"Error: {message}")
        messagebox.showerror("Error", message)
    
    # Tool actions
    def apply_rounding(self):
        """Apply rounding to edges."""
        self.edge_type.set("Rounded")
        if self.edge_size_var.get() == "0":
            self.edge_size_var.set("2")
        self.update_preview()
    
    def apply_chamfer(self):
        """Apply chamfer to edges."""
        self.edge_type.set("Chamfered")
        if self.edge_size_var.get() == "0":
            self.edge_size_var.set("2")
        self.update_preview()
    
    def reset_edges(self):
        """Reset edges to sharp."""
        self.edge_type.set("Sharp")
        self.edge_size_var.set("0")
        self.update_preview()
    
    # File operations
    def new_project(self):
        """Create new project."""
        self.setup_default_values()
        self.update_preview()
        self.status_var.set("New project created")
    
    def open_project(self):
        """Open project file."""
        messagebox.showinfo("Info", "Project loading will be implemented in future version")
    
    def save_project(self):
        """Save project file."""
        messagebox.showinfo("Info", "Project saving will be implemented in future version")
    
    def export_stl(self):
        """Export current model as STL."""
        if self.current_mesh is None:
            messagebox.showwarning("Warning", "No model to export")
            return
        
        try:
            filepath = filedialog.asksaveasfilename(
                defaultextension=".stl",
                filetypes=[("STL files", "*.stl"), ("All files", "*.*")],
                title="Export STL File"
            )
            
            if filepath:
                self.current_mesh.export(filepath)
                messagebox.showinfo("Success", f"Model exported successfully:\n{filepath}")
                self.status_var.set(f"Exported: {os.path.basename(filepath)}")
                
        except Exception as e:
            messagebox.showerror("Export Error", str(e))
    
    # View operations
    def reset_view(self):
        """Reset 3D view to default."""
        self.viewer.reset_view()
    
    def fit_view(self):
        """Fit model in view."""
        self.viewer.fit_view()

def main():
    """Main application entry point."""
    root = tk.Tk()
    app = BambuStudioApp(root)
    
    # Handle window close
    def on_closing():
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
