import trimesh

def buat_kotak_berdinding(panjang, lebar, tinggi, ketebalan):
    """
    Membuat kotak 3D berbentuk wadah terbuka (dinding seragam), menggunakan
    perbedaan dua kotak (luar dan dalam).
    """
    if ketebalan * 2 >= min(panjang, lebar):
        raise ValueError("Ketebalan terlalu besar untuk ukuran kotak.")
    if ketebalan >= tinggi:
        raise ValueError("Ketebalan tidak boleh melebihi tinggi kotak.")

    # Kotak luar (dinding luar)
    kotak_luar = trimesh.creation.box(extents=[panjang, lebar, tinggi])
    kotak_luar.apply_translation([panjang / 2, lebar / 2, tinggi / 2])

    # Ukuran cavity bagian dalam (isi kosong)
    panjang_dalam = panjang - 2 * ketebalan
    lebar_dalam = lebar - 2 * ketebalan
    tinggi_dalam = tinggi - ketebalan

    # Kotak dalam, posisi cavity
    cavity = trimesh.creation.box(extents=[panjang_dalam, lebar_dalam, tinggi_dalam])
    cavity.apply_translation([
        ketebalan + panjang_dalam / 2,
        ketebalan + lebar_dalam / 2,
        ketebalan + tinggi_dalam / 2
    ])

    # Hollow box = luar - dalam
    kotak_berlubang = kotak_luar.difference(cavity)

    return kotak_berlubang

def simpan_stl(mesh, nama_file):
    mesh.export(nama_file)
