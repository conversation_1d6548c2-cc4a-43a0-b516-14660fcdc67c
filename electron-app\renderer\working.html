<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solutronics - 3D Container Generator</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2b2b2b;
            padding: 20px;
            border-right: 1px solid #404040;
            overflow-y: auto;
        }
        
        .viewport {
            flex: 1;
            position: relative;
            background: #0f0f0f;
        }
        
        #canvas3d {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .controls h3 {
            color: #4A90E2;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .shape-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #404040;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .shape-btn:hover {
            background: #4A90E2;
        }
        
        .shape-btn.active {
            background: #4A90E2;
        }
        
        .param-group {
            margin-bottom: 15px;
        }
        
        .param-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #cccccc;
        }
        
        .param-group input {
            width: 100%;
            padding: 5px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 3px;
            color: white;
        }
        
        .export-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4A90E2, #FF6B35);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }
        
        .export-btn:hover {
            opacity: 0.9;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
        }
        
        .debug {
            position: absolute;
            top: 50px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 100;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="controls">
                <h3>🔧 Shape Tools</h3>
                <button class="shape-btn active" id="boxBtn">📦 Box</button>
                <button class="shape-btn" id="cylinderBtn">🥫 Cylinder</button>
                <button class="shape-btn" id="prismBtn">🔺 Prism</button>
            </div>
            
            <div class="controls">
                <h3>📏 Dimensions</h3>
                <div class="param-group">
                    <label>Length/Diameter (mm)</label>
                    <input type="number" id="length" value="100" min="1" max="1000">
                </div>
                <div class="param-group" id="widthGroup">
                    <label>Width (mm)</label>
                    <input type="number" id="width" value="60" min="1" max="1000">
                </div>
                <div class="param-group">
                    <label>Height (mm)</label>
                    <input type="number" id="height" value="40" min="1" max="1000">
                </div>
                <div class="param-group">
                    <label>Wall Thickness (mm)</label>
                    <input type="number" id="thickness" value="3" min="0.1" max="50" step="0.1">
                </div>
            </div>
            
            <div class="controls">
                <h3>⚙️ Tools</h3>
                <button class="shape-btn" id="resetBtn">🔄 Reset View</button>
                <button class="shape-btn" id="fitBtn">📐 Fit View</button>
            </div>
            
            <button class="export-btn" id="exportBtn">💾 Export STL</button>
        </div>
        
        <div class="viewport">
            <canvas id="canvas3d"></canvas>
            <div class="status" id="status">Initializing...</div>
            <div class="debug" id="debug">Debug info will appear here</div>
        </div>
    </div>

    <!-- Three.js with proper importmap -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        // Global variables
        let scene, camera, renderer, controls;
        let currentMesh = null;
        let currentShape = 'box';
        
        // Debug function
        function debug(message) {
            console.log(message);
            const debugEl = document.getElementById('debug');
            if (debugEl) {
                debugEl.innerHTML += message + '<br>';
                debugEl.scrollTop = debugEl.scrollHeight;
            }
        }
        
        // Initialize Three.js
        async function init() {
            try {
                debug('🎯 Starting 3D Container Generator');
                updateStatus('Initializing Three.js...');
                
                // Check Three.js
                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js not loaded');
                }
                debug('✅ Three.js loaded: ' + THREE.REVISION);
                
                // Get canvas
                const canvas = document.getElementById('canvas3d');
                if (!canvas) {
                    throw new Error('Canvas not found');
                }
                debug('✅ Canvas found');
                
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x0f0f0f);
                debug('✅ Scene created');
                
                // Create camera
                const aspect = canvas.clientWidth / canvas.clientHeight;
                camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
                camera.position.set(150, 100, 150);
                debug('✅ Camera created');
                
                // Create renderer
                renderer = new THREE.WebGLRenderer({ 
                    canvas: canvas, 
                    antialias: true,
                    alpha: true
                });
                renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                debug('✅ Renderer created');
                
                // Create controls
                controls = new OrbitControls(camera, canvas);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.screenSpacePanning = false;
                controls.minDistance = 10;
                controls.maxDistance = 500;
                debug('✅ Controls created');
                
                // Add lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(100, 100, 50);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                scene.add(directionalLight);
                debug('✅ Lighting added');
                
                // Add grid
                const gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
                scene.add(gridHelper);
                
                // Add axes
                const axesHelper = new THREE.AxesHelper(50);
                scene.add(axesHelper);
                debug('✅ Helpers added');
                
                // Setup event listeners
                setupEventListeners();
                debug('✅ Event listeners setup');
                
                // Handle resize
                window.addEventListener('resize', onWindowResize);
                
                // Generate initial geometry
                updateGeometry();
                
                // Start render loop
                animate();
                debug('✅ Animation started');
                
                updateStatus('✅ Ready - Click shapes to generate containers');
                
            } catch (error) {
                debug('❌ Initialization failed: ' + error.message);
                updateStatus('❌ Initialization failed: ' + error.message);
                console.error('Initialization error:', error);
            }
        }
        
        function setupEventListeners() {
            // Shape buttons
            document.getElementById('boxBtn').addEventListener('click', () => selectShape('box'));
            document.getElementById('cylinderBtn').addEventListener('click', () => selectShape('cylinder'));
            document.getElementById('prismBtn').addEventListener('click', () => selectShape('prism'));
            
            // Parameter inputs
            document.getElementById('length').addEventListener('input', updateGeometry);
            document.getElementById('width').addEventListener('input', updateGeometry);
            document.getElementById('height').addEventListener('input', updateGeometry);
            document.getElementById('thickness').addEventListener('input', updateGeometry);
            
            // Tool buttons
            document.getElementById('resetBtn').addEventListener('click', resetView);
            document.getElementById('fitBtn').addEventListener('click', fitView);
            document.getElementById('exportBtn').addEventListener('click', exportSTL);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }
        
        function onWindowResize() {
            if (!camera || !renderer) return;
            
            const canvas = document.getElementById('canvas3d');
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        }
        
        function selectShape(shape) {
            currentShape = shape;
            debug(`🔧 Selected shape: ${shape}`);
            
            // Update button states
            document.querySelectorAll('.shape-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(shape + 'Btn').classList.add('active');
            
            // Show/hide width input for cylinder
            const widthGroup = document.getElementById('widthGroup');
            widthGroup.style.display = shape === 'cylinder' ? 'none' : 'block';
            
            updateGeometry();
        }
        
        // Hollow geometry creation functions
        function createHollowBoxGeometry(length, width, height, wallThickness) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            // Ensure minimum wall thickness
            const t = Math.min(wallThickness, Math.min(length, width, height) / 4);

            // Outer dimensions
            const l = length, w = width, h = height;

            // Inner dimensions
            const il = Math.max(0, l - 2 * t);
            const iw = Math.max(0, w - 2 * t);
            const ih = Math.max(0, h - t); // Open top

            // Create vertices for hollow box
            // Outer vertices (bottom face)
            vertices.push(
                -l/2, 0, -w/2,    // 0: outer bottom-left-front
                l/2, 0, -w/2,     // 1: outer bottom-right-front
                l/2, 0, w/2,      // 2: outer bottom-right-back
                -l/2, 0, w/2,     // 3: outer bottom-left-back
            );

            // Outer vertices (top face)
            vertices.push(
                -l/2, h, -w/2,    // 4: outer top-left-front
                l/2, h, -w/2,     // 5: outer top-right-front
                l/2, h, w/2,      // 6: outer top-right-back
                -l/2, h, w/2,     // 7: outer top-left-back
            );

            // Inner vertices (bottom face)
            if (il > 0 && iw > 0 && ih > 0) {
                vertices.push(
                    -il/2, t, -iw/2,    // 8: inner bottom-left-front
                    il/2, t, -iw/2,     // 9: inner bottom-right-front
                    il/2, t, iw/2,      // 10: inner bottom-right-back
                    -il/2, t, iw/2,     // 11: inner bottom-left-back
                );

                // Inner vertices (top face - open)
                vertices.push(
                    -il/2, h, -iw/2,    // 12: inner top-left-front
                    il/2, h, -iw/2,     // 13: inner top-right-front
                    il/2, h, iw/2,      // 14: inner top-right-back
                    -il/2, h, iw/2,     // 15: inner top-left-back
                );
            }

            // Create faces for hollow box
            // Bottom face (outer)
            indices.push(0, 1, 2, 0, 2, 3);

            // Side faces (outer)
            indices.push(
                // Front face
                0, 4, 5, 0, 5, 1,
                // Right face
                1, 5, 6, 1, 6, 2,
                // Back face
                2, 6, 7, 2, 7, 3,
                // Left face
                3, 7, 4, 3, 4, 0
            );

            if (il > 0 && iw > 0 && ih > 0) {
                // Inner bottom face
                indices.push(8, 10, 9, 8, 11, 10);

                // Inner side faces
                indices.push(
                    // Inner front face
                    8, 9, 13, 8, 13, 12,
                    // Inner right face
                    9, 10, 14, 9, 14, 13,
                    // Inner back face
                    10, 11, 15, 10, 15, 14,
                    // Inner left face
                    11, 8, 12, 11, 12, 15
                );

                // Connect outer and inner (walls)
                // Bottom walls
                indices.push(
                    // Front wall bottom
                    0, 8, 9, 0, 9, 1,
                    // Right wall bottom
                    1, 9, 10, 1, 10, 2,
                    // Back wall bottom
                    2, 10, 11, 2, 11, 3,
                    // Left wall bottom
                    3, 11, 8, 3, 8, 0
                );

                // Top rim (connecting outer top to inner top)
                indices.push(
                    // Front rim
                    4, 12, 13, 4, 13, 5,
                    // Right rim
                    5, 13, 14, 5, 14, 6,
                    // Back rim
                    6, 14, 15, 6, 15, 7,
                    // Left rim
                    7, 15, 12, 7, 12, 4
                );
            } else {
                // Solid top if inner dimensions are too small
                indices.push(4, 6, 5, 4, 7, 6);
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();

            return geometry;
        }

        function createHollowCylinderGeometry(radius, height, wallThickness, segments) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            // Ensure minimum wall thickness
            const t = Math.min(wallThickness, radius / 2);
            const innerRadius = Math.max(0, radius - t);
            const innerHeight = Math.max(0, height - t); // Open top

            // Create vertices
            // Outer cylinder bottom
            vertices.push(0, 0, 0); // Center bottom (index 0)
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(
                    Math.cos(angle) * radius,
                    0,
                    Math.sin(angle) * radius
                );
            }

            // Outer cylinder top
            vertices.push(0, height, 0); // Center top (index segments + 1)
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );
            }

            // Inner cylinder (if thick enough)
            if (innerRadius > 0 && innerHeight > 0) {
                // Inner bottom
                vertices.push(0, t, 0); // Inner center bottom
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(
                        Math.cos(angle) * innerRadius,
                        t,
                        Math.sin(angle) * innerRadius
                    );
                }

                // Inner top (open)
                vertices.push(0, height, 0); // Inner center top
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(
                        Math.cos(angle) * innerRadius,
                        height,
                        Math.sin(angle) * innerRadius
                    );
                }
            }

            // Create faces
            // Outer bottom face
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                indices.push(0, i + 1, next + 1);
            }

            // Outer side faces
            const outerTopStart = segments + 2;
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                const bottom1 = i + 1;
                const bottom2 = next + 1;
                const top1 = outerTopStart + i;
                const top2 = outerTopStart + next;

                indices.push(bottom1, top1, top2);
                indices.push(bottom1, top2, bottom2);
            }

            if (innerRadius > 0 && innerHeight > 0) {
                const innerBottomStart = segments * 2 + 3;
                const innerTopStart = segments * 3 + 4;

                // Inner bottom face (reversed winding)
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(innerBottomStart, innerBottomStart + next, innerBottomStart + i);
                }

                // Inner side faces (reversed winding)
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const bottom1 = innerBottomStart + i;
                    const bottom2 = innerBottomStart + next;
                    const top1 = innerTopStart + i;
                    const top2 = innerTopStart + next;

                    indices.push(bottom1, top2, top1);
                    indices.push(bottom1, bottom2, top2);
                }

                // Bottom wall (connecting outer and inner)
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = i + 1;
                    const outer2 = next + 1;
                    const inner1 = innerBottomStart + i;
                    const inner2 = innerBottomStart + next;

                    indices.push(outer1, inner1, inner2);
                    indices.push(outer1, inner2, outer2);
                }

                // Top rim (connecting outer and inner at top)
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = outerTopStart + i;
                    const outer2 = outerTopStart + next;
                    const inner1 = innerTopStart + i;
                    const inner2 = innerTopStart + next;

                    indices.push(outer1, inner2, inner1);
                    indices.push(outer1, outer2, inner2);
                }
            } else {
                // Solid top if inner dimensions are too small
                const topStart = segments + 2;
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(segments + 1, topStart + next, topStart + i);
                }
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();

            return geometry;
        }

        function updateGeometry() {
            try {
                debug('🔧 Generating geometry...');
                updateStatus('🔧 Generating geometry...');
                
                // Remove existing mesh
                if (currentMesh) {
                    scene.remove(currentMesh);
                    currentMesh.geometry.dispose();
                    currentMesh.material.dispose();
                    currentMesh = null;
                }
                
                // Get parameters
                const length = parseFloat(document.getElementById('length').value) || 100;
                const width = parseFloat(document.getElementById('width').value) || 60;
                const height = parseFloat(document.getElementById('height').value) || 40;
                const thickness = parseFloat(document.getElementById('thickness').value) || 3;
                
                debug(`Parameters: L=${length}, W=${width}, H=${height}, T=${thickness}`);
                
                // Create hollow container geometry
                let geometry;

                switch (currentShape) {
                    case 'box':
                        geometry = createHollowBoxGeometry(length, width, height, thickness);
                        break;
                    case 'cylinder':
                        const radius = length / 2;
                        geometry = createHollowCylinderGeometry(radius, height, thickness, 32);
                        break;
                    case 'prism':
                        const prismRadius = length / (2 * Math.sin(Math.PI / 6));
                        geometry = createHollowCylinderGeometry(prismRadius, height, thickness, 6);
                        break;
                    default:
                        geometry = createHollowBoxGeometry(length, width, height, thickness);
                }
                
                // Create material
                const material = new THREE.MeshPhongMaterial({
                    color: 0x4A90E2,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });
                
                // Create mesh
                currentMesh = new THREE.Mesh(geometry, material);
                currentMesh.castShadow = true;
                currentMesh.receiveShadow = true;
                
                // Add to scene
                scene.add(currentMesh);
                
                const vertexCount = geometry.attributes.position.count;
                debug(`✅ ${currentShape} generated - ${vertexCount} vertices`);
                updateStatus(`✅ ${currentShape} generated - ${vertexCount} vertices`);
                
            } catch (error) {
                debug('❌ Geometry generation failed: ' + error.message);
                updateStatus('❌ Geometry generation failed');
                console.error('Geometry error:', error);
            }
        }
        
        function resetView() {
            if (!camera || !controls) return;
            
            camera.position.set(150, 100, 150);
            camera.lookAt(0, 0, 0);
            controls.reset();
            debug('🔄 View reset');
            updateStatus('🔄 View reset');
        }
        
        function fitView() {
            if (!currentMesh || !camera || !controls) return;
            
            try {
                const box = new THREE.Box3().setFromObject(currentMesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                const maxDim = Math.max(size.x, size.y, size.z);
                const distance = maxDim * 2;
                
                camera.position.copy(center);
                camera.position.x += distance;
                camera.position.y += distance * 0.5;
                camera.position.z += distance;
                camera.lookAt(center);
                
                controls.target.copy(center);
                controls.update();
                
                debug('📐 View fitted to object');
                updateStatus('📐 View fitted to object');
                
            } catch (error) {
                debug('❌ Fit view failed: ' + error.message);
                updateStatus('❌ Fit view failed');
            }
        }
        
        function exportSTL() {
            if (!currentMesh) {
                debug('❌ No geometry to export');
                updateStatus('❌ No geometry to export');
                return;
            }

            try {
                debug('💾 Exporting STL...');
                updateStatus('💾 Exporting STL...');

                const geometry = currentMesh.geometry;
                const positionAttribute = geometry.attributes.position;
                const indexAttribute = geometry.index;

                let stl = 'solid container\n';
                let triangleCount = 0;

                if (indexAttribute) {
                    // Indexed geometry - process triangles using indices
                    for (let i = 0; i < indexAttribute.count; i += 3) {
                        const i1 = indexAttribute.getX(i);
                        const i2 = indexAttribute.getX(i + 1);
                        const i3 = indexAttribute.getX(i + 2);

                        const v1 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i1);
                        const v2 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i2);
                        const v3 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i3);

                        // Calculate normal
                        const normal = new THREE.Vector3();
                        const edge1 = new THREE.Vector3().subVectors(v2, v1);
                        const edge2 = new THREE.Vector3().subVectors(v3, v1);
                        normal.crossVectors(edge1, edge2).normalize();

                        // Skip degenerate triangles
                        if (normal.length() > 0.001) {
                            stl += `  facet normal ${normal.x.toFixed(6)} ${normal.y.toFixed(6)} ${normal.z.toFixed(6)}\n`;
                            stl += `    outer loop\n`;
                            stl += `      vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                            stl += `      vertex ${v2.x.toFixed(6)} ${v2.y.toFixed(6)} ${v2.z.toFixed(6)}\n`;
                            stl += `      vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                            stl += `    endloop\n`;
                            stl += `  endfacet\n`;
                            triangleCount++;
                        }
                    }
                } else {
                    // Non-indexed geometry - process triangles directly
                    for (let i = 0; i < positionAttribute.count; i += 3) {
                        const v1 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i);
                        const v2 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i + 1);
                        const v3 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i + 2);

                        // Calculate normal
                        const normal = new THREE.Vector3();
                        const edge1 = new THREE.Vector3().subVectors(v2, v1);
                        const edge2 = new THREE.Vector3().subVectors(v3, v1);
                        normal.crossVectors(edge1, edge2).normalize();

                        // Skip degenerate triangles
                        if (normal.length() > 0.001) {
                            stl += `  facet normal ${normal.x.toFixed(6)} ${normal.y.toFixed(6)} ${normal.z.toFixed(6)}\n`;
                            stl += `    outer loop\n`;
                            stl += `      vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                            stl += `      vertex ${v2.x.toFixed(6)} ${v2.y.toFixed(6)} ${v2.z.toFixed(6)}\n`;
                            stl += `      vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                            stl += `    endloop\n`;
                            stl += `  endfacet\n`;
                            triangleCount++;
                        }
                    }
                }

                stl += 'endsolid container\n';

                debug(`✅ STL generated with ${triangleCount} triangles`);

                // Download file
                const blob = new Blob([stl], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${currentShape}_container.stl`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                debug('✅ STL exported successfully');
                updateStatus(`✅ STL exported - ${triangleCount} triangles`);

            } catch (error) {
                debug('❌ STL export failed: ' + error.message);
                updateStatus('❌ STL export failed');
                console.error('Export error:', error);
            }
        }
        
        function updateStatus(message) {
            const statusEl = document.getElementById('status');
            if (statusEl) {
                statusEl.textContent = message;
            }
            console.log(message);
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
        
    </script>
</body>
</html>
