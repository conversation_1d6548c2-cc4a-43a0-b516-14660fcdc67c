<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Container Generator - Bambu Studio Style</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2b2b2b;
            padding: 20px;
            border-right: 1px solid #404040;
            overflow-y: auto;
        }
        
        .viewport {
            flex: 1;
            position: relative;
            background: #0f0f0f;
        }
        
        #canvas3d {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .controls h3 {
            color: #4A90E2;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .shape-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #404040;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .shape-btn:hover {
            background: #4A90E2;
        }
        
        .shape-btn.active {
            background: #4A90E2;
        }
        
        .param-group {
            margin-bottom: 15px;
        }
        
        .param-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #cccccc;
        }
        
        .param-group input {
            width: 100%;
            padding: 5px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 3px;
            color: white;
        }
        
        .export-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4A90E2, #FF6B35);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }
        
        .export-btn:hover {
            opacity: 0.9;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
        }
        
        .debug {
            position: absolute;
            top: 50px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 100;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="controls">
                <h3>🔧 Shape Tools</h3>
                <button class="shape-btn active" id="boxBtn">📦 Box</button>
                <button class="shape-btn" id="cylinderBtn">🥫 Cylinder</button>
                <button class="shape-btn" id="prismBtn">🔺 Prism</button>
            </div>
            
            <div class="controls">
                <h3>📏 Dimensions</h3>
                <div class="param-group">
                    <label>Length/Diameter (mm)</label>
                    <input type="number" id="length" value="100" min="1" max="1000">
                </div>
                <div class="param-group" id="widthGroup">
                    <label>Width (mm)</label>
                    <input type="number" id="width" value="60" min="1" max="1000">
                </div>
                <div class="param-group">
                    <label>Height (mm)</label>
                    <input type="number" id="height" value="40" min="1" max="1000">
                </div>
                <div class="param-group">
                    <label>Wall Thickness (mm)</label>
                    <input type="number" id="thickness" value="3" min="0.1" max="50" step="0.1">
                </div>
            </div>
            
            <div class="controls">
                <h3>⚙️ Tools</h3>
                <button class="shape-btn" id="resetBtn">🔄 Reset View</button>
                <button class="shape-btn" id="fitBtn">📐 Fit View</button>
            </div>
            
            <button class="export-btn" id="exportBtn">💾 Export STL</button>
        </div>
        
        <div class="viewport">
            <canvas id="canvas3d"></canvas>
            <div class="status" id="status">Initializing...</div>
            <div class="debug" id="debug">Debug info will appear here</div>
        </div>
    </div>

    <!-- Three.js with proper importmap -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        // Global variables
        let scene, camera, renderer, controls;
        let currentMesh = null;
        let currentShape = 'box';
        
        // Debug function
        function debug(message) {
            console.log(message);
            const debugEl = document.getElementById('debug');
            if (debugEl) {
                debugEl.innerHTML += message + '<br>';
                debugEl.scrollTop = debugEl.scrollHeight;
            }
        }
        
        // Initialize Three.js
        async function init() {
            try {
                debug('🎯 Starting 3D Container Generator');
                updateStatus('Initializing Three.js...');
                
                // Check Three.js
                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js not loaded');
                }
                debug('✅ Three.js loaded: ' + THREE.REVISION);
                
                // Get canvas
                const canvas = document.getElementById('canvas3d');
                if (!canvas) {
                    throw new Error('Canvas not found');
                }
                debug('✅ Canvas found');
                
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x0f0f0f);
                debug('✅ Scene created');
                
                // Create camera
                const aspect = canvas.clientWidth / canvas.clientHeight;
                camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
                camera.position.set(150, 100, 150);
                debug('✅ Camera created');
                
                // Create renderer
                renderer = new THREE.WebGLRenderer({ 
                    canvas: canvas, 
                    antialias: true,
                    alpha: true
                });
                renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                debug('✅ Renderer created');
                
                // Create controls
                controls = new OrbitControls(camera, canvas);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.screenSpacePanning = false;
                controls.minDistance = 10;
                controls.maxDistance = 500;
                debug('✅ Controls created');
                
                // Add lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(100, 100, 50);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                scene.add(directionalLight);
                debug('✅ Lighting added');
                
                // Add grid
                const gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
                scene.add(gridHelper);
                
                // Add axes
                const axesHelper = new THREE.AxesHelper(50);
                scene.add(axesHelper);
                debug('✅ Helpers added');
                
                // Setup event listeners
                setupEventListeners();
                debug('✅ Event listeners setup');
                
                // Handle resize
                window.addEventListener('resize', onWindowResize);
                
                // Generate initial geometry
                updateGeometry();
                
                // Start render loop
                animate();
                debug('✅ Animation started');
                
                updateStatus('✅ Ready - Click shapes to generate containers');
                
            } catch (error) {
                debug('❌ Initialization failed: ' + error.message);
                updateStatus('❌ Initialization failed: ' + error.message);
                console.error('Initialization error:', error);
            }
        }
        
        function setupEventListeners() {
            // Shape buttons
            document.getElementById('boxBtn').addEventListener('click', () => selectShape('box'));
            document.getElementById('cylinderBtn').addEventListener('click', () => selectShape('cylinder'));
            document.getElementById('prismBtn').addEventListener('click', () => selectShape('prism'));
            
            // Parameter inputs
            document.getElementById('length').addEventListener('input', updateGeometry);
            document.getElementById('width').addEventListener('input', updateGeometry);
            document.getElementById('height').addEventListener('input', updateGeometry);
            document.getElementById('thickness').addEventListener('input', updateGeometry);
            
            // Tool buttons
            document.getElementById('resetBtn').addEventListener('click', resetView);
            document.getElementById('fitBtn').addEventListener('click', fitView);
            document.getElementById('exportBtn').addEventListener('click', exportSTL);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }
        
        function onWindowResize() {
            if (!camera || !renderer) return;
            
            const canvas = document.getElementById('canvas3d');
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        }
        
        function selectShape(shape) {
            currentShape = shape;
            debug(`🔧 Selected shape: ${shape}`);
            
            // Update button states
            document.querySelectorAll('.shape-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(shape + 'Btn').classList.add('active');
            
            // Show/hide width input for cylinder
            const widthGroup = document.getElementById('widthGroup');
            widthGroup.style.display = shape === 'cylinder' ? 'none' : 'block';
            
            updateGeometry();
        }
        
        function updateGeometry() {
            try {
                debug('🔧 Generating geometry...');
                updateStatus('🔧 Generating geometry...');
                
                // Remove existing mesh
                if (currentMesh) {
                    scene.remove(currentMesh);
                    currentMesh.geometry.dispose();
                    currentMesh.material.dispose();
                    currentMesh = null;
                }
                
                // Get parameters
                const length = parseFloat(document.getElementById('length').value) || 100;
                const width = parseFloat(document.getElementById('width').value) || 60;
                const height = parseFloat(document.getElementById('height').value) || 40;
                const thickness = parseFloat(document.getElementById('thickness').value) || 3;
                
                debug(`Parameters: L=${length}, W=${width}, H=${height}, T=${thickness}`);
                
                // Create geometry
                let geometry;
                
                switch (currentShape) {
                    case 'box':
                        geometry = new THREE.BoxGeometry(length, height, width);
                        break;
                    case 'cylinder':
                        const radius = length / 2;
                        geometry = new THREE.CylinderGeometry(radius, radius, height, 32);
                        break;
                    case 'prism':
                        const prismRadius = length / (2 * Math.sin(Math.PI / 6));
                        geometry = new THREE.CylinderGeometry(prismRadius, prismRadius, height, 6);
                        break;
                    default:
                        geometry = new THREE.BoxGeometry(length, height, width);
                }
                
                // Create material
                const material = new THREE.MeshPhongMaterial({
                    color: 0x4A90E2,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });
                
                // Create mesh
                currentMesh = new THREE.Mesh(geometry, material);
                currentMesh.castShadow = true;
                currentMesh.receiveShadow = true;
                
                // Add to scene
                scene.add(currentMesh);
                
                const vertexCount = geometry.attributes.position.count;
                debug(`✅ ${currentShape} generated - ${vertexCount} vertices`);
                updateStatus(`✅ ${currentShape} generated - ${vertexCount} vertices`);
                
            } catch (error) {
                debug('❌ Geometry generation failed: ' + error.message);
                updateStatus('❌ Geometry generation failed');
                console.error('Geometry error:', error);
            }
        }
        
        function resetView() {
            if (!camera || !controls) return;
            
            camera.position.set(150, 100, 150);
            camera.lookAt(0, 0, 0);
            controls.reset();
            debug('🔄 View reset');
            updateStatus('🔄 View reset');
        }
        
        function fitView() {
            if (!currentMesh || !camera || !controls) return;
            
            try {
                const box = new THREE.Box3().setFromObject(currentMesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                const maxDim = Math.max(size.x, size.y, size.z);
                const distance = maxDim * 2;
                
                camera.position.copy(center);
                camera.position.x += distance;
                camera.position.y += distance * 0.5;
                camera.position.z += distance;
                camera.lookAt(center);
                
                controls.target.copy(center);
                controls.update();
                
                debug('📐 View fitted to object');
                updateStatus('📐 View fitted to object');
                
            } catch (error) {
                debug('❌ Fit view failed: ' + error.message);
                updateStatus('❌ Fit view failed');
            }
        }
        
        function exportSTL() {
            if (!currentMesh) {
                debug('❌ No geometry to export');
                updateStatus('❌ No geometry to export');
                return;
            }
            
            try {
                debug('💾 Exporting STL...');
                updateStatus('💾 Exporting STL...');
                
                // Simple STL export
                const geometry = currentMesh.geometry;
                const positionAttribute = geometry.attributes.position;
                
                let stl = 'solid container\n';
                
                // Process triangles
                for (let i = 0; i < positionAttribute.count; i += 3) {
                    const v1 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i);
                    const v2 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i + 1);
                    const v3 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i + 2);
                    
                    // Calculate normal
                    const normal = new THREE.Vector3();
                    const edge1 = new THREE.Vector3().subVectors(v2, v1);
                    const edge2 = new THREE.Vector3().subVectors(v3, v1);
                    normal.crossVectors(edge1, edge2).normalize();
                    
                    stl += `  facet normal ${normal.x} ${normal.y} ${normal.z}\n`;
                    stl += `    outer loop\n`;
                    stl += `      vertex ${v1.x} ${v1.y} ${v1.z}\n`;
                    stl += `      vertex ${v2.x} ${v2.y} ${v2.z}\n`;
                    stl += `      vertex ${v3.x} ${v3.y} ${v3.z}\n`;
                    stl += `    endloop\n`;
                    stl += `  endfacet\n`;
                }
                
                stl += 'endsolid container\n';
                
                // Download file
                const blob = new Blob([stl], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${currentShape}_container.stl`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                debug('✅ STL exported successfully');
                updateStatus('✅ STL exported successfully');
                
            } catch (error) {
                debug('❌ STL export failed: ' + error.message);
                updateStatus('❌ STL export failed');
                console.error('Export error:', error);
            }
        }
        
        function updateStatus(message) {
            const statusEl = document.getElementById('status');
            if (statusEl) {
                statusEl.textContent = message;
            }
            console.log(message);
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
        
    </script>
</body>
</html>
