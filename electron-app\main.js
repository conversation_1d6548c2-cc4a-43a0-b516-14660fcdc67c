const {
  app,
  BrowserWindow,
  Menu,
  dialog,
  ipcMain,
  shell,
} = require("electron");
const path = require("path");
const fs = require("fs");

// Keep a global reference of the window object
let mainWindow;
let isDev = process.argv.includes("--dev");

function createWindow() {
  // Create the browser window with Bambu Studio styling
  mainWindow = new BrowserWindow({
    width: 1600,
    height: 1000,
    minWidth: 1200,
    minHeight: 800,
    icon: path.join(__dirname, "assets", "icon.png"),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, "preload.js"),
      webSecurity: true,
    },
    titleBarStyle: "default",
    backgroundColor: "#1a1a1a",
    show: false, // Don't show until ready
    frame: true,
    resizable: true,
    maximizable: true,
    fullscreenable: true,
  });

  // Load the app
  mainWindow.loadFile("renderer/complete-suite.html");

  // Show window when ready to prevent visual flash
  mainWindow.once("ready-to-show", () => {
    mainWindow.show();

    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on("closed", () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: "deny" };
  });

  // Prevent navigation to external sites
  mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (parsedUrl.origin !== "file://") {
      event.preventDefault();
    }
  });
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// Create application menu
function createMenu() {
  const template = [
    {
      label: "File",
      submenu: [
        {
          label: "New Project",
          accelerator: "CmdOrCtrl+N",
          click: () => {
            mainWindow.webContents.send("menu-new-project");
          },
        },
        {
          label: "Open Project",
          accelerator: "CmdOrCtrl+O",
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ["openFile"],
              filters: [
                { name: "Container Projects", extensions: ["json"] },
                { name: "All Files", extensions: ["*"] },
              ],
            });

            if (!result.canceled) {
              mainWindow.webContents.send(
                "menu-open-project",
                result.filePaths[0]
              );
            }
          },
        },
        {
          label: "Save Project",
          accelerator: "CmdOrCtrl+S",
          click: () => {
            mainWindow.webContents.send("menu-save-project");
          },
        },
        { type: "separator" },
        {
          label: "Export STL",
          accelerator: "CmdOrCtrl+E",
          click: () => {
            mainWindow.webContents.send("menu-export-stl");
          },
        },
        { type: "separator" },
        {
          label: "Exit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: "Edit",
      submenu: [
        { role: "undo" },
        { role: "redo" },
        { type: "separator" },
        { role: "cut" },
        { role: "copy" },
        { role: "paste" },
      ],
    },
    {
      label: "View",
      submenu: [
        {
          label: "Reset View",
          accelerator: "CmdOrCtrl+R",
          click: () => {
            mainWindow.webContents.send("menu-reset-view");
          },
        },
        {
          label: "Fit View",
          accelerator: "CmdOrCtrl+F",
          click: () => {
            mainWindow.webContents.send("menu-fit-view");
          },
        },
        { type: "separator" },
        {
          label: "Toggle Grid",
          accelerator: "CmdOrCtrl+G",
          click: () => {
            mainWindow.webContents.send("menu-toggle-grid");
          },
        },
        {
          label: "Toggle Axes",
          accelerator: "CmdOrCtrl+A",
          click: () => {
            mainWindow.webContents.send("menu-toggle-axes");
          },
        },
        { type: "separator" },
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "resetZoom" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" },
      ],
    },
    {
      label: "Tools",
      submenu: [
        {
          label: "Round Edges",
          click: () => {
            mainWindow.webContents.send("menu-round-edges");
          },
        },
        {
          label: "Chamfer Edges",
          click: () => {
            mainWindow.webContents.send("menu-chamfer-edges");
          },
        },
        {
          label: "Reset Edges",
          click: () => {
            mainWindow.webContents.send("menu-reset-edges");
          },
        },
      ],
    },
    {
      label: "Help",
      submenu: [
        {
          label: "About",
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: "info",
              title: "About Bambu Container Generator",
              message: "Bambu Studio Style 3D Container Generator",
              detail:
                "Version 1.0.0\n\nProfessional 3D container modeling application\ninspired by Bambu Lab Studio.\n\nFeatures:\n• Three.js 3D visualization\n• Interactive container generation\n• Edge manipulation tools\n• STL export for 3D printing",
            });
          },
        },
        {
          label: "Keyboard Shortcuts",
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: "info",
              title: "Keyboard Shortcuts",
              message: "Keyboard Shortcuts",
              detail:
                "File:\nCtrl+N - New Project\nCtrl+O - Open Project\nCtrl+S - Save Project\nCtrl+E - Export STL\n\nView:\nCtrl+R - Reset View\nCtrl+F - Fit View\nCtrl+G - Toggle Grid\nCtrl+A - Toggle Axes\n\n3D Controls:\nLeft Click + Drag - Rotate\nRight Click + Drag - Pan\nMouse Wheel - Zoom",
            });
          },
        },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers for file operations
ipcMain.handle("save-file-dialog", async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle("open-file-dialog", async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle("save-file", async (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle("read-file", async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, "utf8");
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handle app updates and notifications
ipcMain.handle("show-notification", async (event, title, body) => {
  new Notification({ title, body }).show();
});

ipcMain.handle("show-error", async (event, title, content) => {
  dialog.showErrorBox(title, content);
});

ipcMain.handle("show-info", async (event, title, content) => {
  dialog.showMessageBox(mainWindow, {
    type: "info",
    title,
    message: content,
  });
});

// Development helpers
if (isDev) {
  console.log("🔧 Running in development mode");

  // Note: electron-reload removed to avoid dependency issues
  // For live reload, manually restart the app during development
}
