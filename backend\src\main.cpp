#include "Application.h"
#include <iostream>
#include <csignal>
#include <cstdlib>

// Global application instance
std::unique_ptr<Application> g_app;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down gracefully..." << std::endl;
    if (g_app) {
        g_app->Shutdown();
    }
    exit(0);
}

void PrintUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]\n"
              << "Options:\n"
              << "  -p, --port PORT       Server port (default: 8081)\n"
              << "  -w, --width WIDTH     Window width (default: 1024)\n"
              << "  -h, --height HEIGHT   Window height (default: 768)\n"
              << "  --headless            Run without GUI window\n"
              << "  --log-level LEVEL     Log level (DEBUG, INFO, WARN, ERROR)\n"
              << "  --help                Show this help message\n"
              << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "🎯 Bambu Studio Style 3D Container Generator" << std::endl;
    std::cout << "=============================================" << std::endl;
    
    // Parse command line arguments
    Application::Config config;
    
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "--help") {
            PrintUsage(argv[0]);
            return 0;
        }
        else if (arg == "-p" || arg == "--port") {
            if (i + 1 < argc) {
                config.serverPort = std::atoi(argv[++i]);
            }
        }
        else if (arg == "-w" || arg == "--width") {
            if (i + 1 < argc) {
                config.windowWidth = std::atoi(argv[++i]);
            }
        }
        else if (arg == "-h" || arg == "--height") {
            if (i + 1 < argc) {
                config.windowHeight = std::atoi(argv[++i]);
            }
        }
        else if (arg == "--headless") {
            config.headless = true;
        }
        else if (arg == "--log-level") {
            if (i + 1 < argc) {
                config.logLevel = argv[++i];
            }
        }
        else {
            std::cerr << "Unknown argument: " << arg << std::endl;
            PrintUsage(argv[0]);
            return 1;
        }
    }
    
    // Setup signal handlers
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    try {
        // Create application
        g_app = std::make_unique<Application>();
        g_app->SetConfig(config);
        
        // Initialize
        std::cout << "🔧 Initializing application..." << std::endl;
        if (!g_app->Initialize()) {
            std::cerr << "❌ Failed to initialize application" << std::endl;
            return 1;
        }
        
        std::cout << "✅ Application initialized successfully" << std::endl;
        std::cout << "🌐 Server running on port " << config.serverPort << std::endl;
        std::cout << "🖥️  Window size: " << config.windowWidth << "x" << config.windowHeight << std::endl;
        
        if (config.headless) {
            std::cout << "👻 Running in headless mode" << std::endl;
        }
        
        std::cout << "\n🚀 Starting main application loop..." << std::endl;
        std::cout << "Press Ctrl+C to stop\n" << std::endl;
        
        // Run main loop
        g_app->Run();
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Unknown exception occurred" << std::endl;
        return 1;
    }
    
    std::cout << "👋 Application shutdown complete" << std::endl;
    return 0;
}
