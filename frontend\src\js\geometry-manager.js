/**
 * Geometry Manager - Handles 3D mesh generation
 * Uses C++ backend when available, falls back to frontend generation
 */

export class GeometryManager {
    constructor(apiClient) {
        this.apiClient = apiClient;
    }
    
    async generateContainer(params) {
        try {
            // Try backend first
            const result = await this.apiClient.generateContainer(params);
            return this.parseMeshData(result);
        } catch (error) {
            console.warn('Backend generation failed, using frontend fallback');
            throw error; // Let caller handle fallback
        }
    }
    
    async generateContainerFrontend(params) {
        console.log('🔧 Generating container using frontend fallback');
        
        switch (params.shapeType) {
            case 'box':
                return this.generateBoxFrontend(params);
            case 'cylinder':
                return this.generateCylinderFrontend(params);
            case 'prism':
                return this.generatePrismFrontend(params);
            default:
                throw new Error(`Unknown shape type: ${params.shapeType}`);
        }
    }
    
    generateBoxFrontend(params) {
        const { length, width, height, wallThickness } = params;
        
        // Create outer box vertices
        const vertices = [];
        const faces = [];
        
        // Outer box vertices (8 vertices)
        const l = length, w = width, h = height;
        vertices.push(
            { x: 0, y: 0, z: 0 },     // 0: bottom-left-front
            { x: l, y: 0, z: 0 },     // 1: bottom-right-front
            { x: l, y: w, z: 0 },     // 2: bottom-right-back
            { x: 0, y: w, z: 0 },     // 3: bottom-left-back
            { x: 0, y: 0, z: h },     // 4: top-left-front
            { x: l, y: 0, z: h },     // 5: top-right-front
            { x: l, y: w, z: h },     // 6: top-right-back
            { x: 0, y: w, z: h }      // 7: top-left-back
        );
        
        // Inner box vertices (8 vertices)
        const t = wallThickness;
        const il = length - 2 * t, iw = width - 2 * t, ih = height - t;
        
        if (il > 0 && iw > 0 && ih > 0) {
            vertices.push(
                { x: t, y: t, z: t },           // 8: inner bottom-left-front
                { x: t + il, y: t, z: t },      // 9: inner bottom-right-front
                { x: t + il, y: t + iw, z: t }, // 10: inner bottom-right-back
                { x: t, y: t + iw, z: t },      // 11: inner bottom-left-back
                { x: t, y: t, z: t + ih },      // 12: inner top-left-front
                { x: t + il, y: t, z: t + ih }, // 13: inner top-right-front
                { x: t + il, y: t + iw, z: t + ih }, // 14: inner top-right-back
                { x: t, y: t + iw, z: t + ih }  // 15: inner top-left-back
            );
            
            // Create faces for hollow box
            this.addHollowBoxFaces(faces);
        } else {
            // Solid box
            this.addSolidBoxFaces(faces);
        }
        
        return {
            vertices: vertices,
            faces: faces,
            volume: this.calculateBoxVolume(params)
        };
    }
    
    generateCylinderFrontend(params) {
        const { length: diameter, height, wallThickness, segments } = params;
        const radius = diameter / 2;
        
        const vertices = [];
        const faces = [];
        
        // Generate outer cylinder
        this.generateCylinderVertices(vertices, 0, 0, radius, height, segments);
        
        // Generate inner cylinder if hollow
        const innerRadius = radius - wallThickness;
        if (innerRadius > 0) {
            const innerStartIndex = vertices.length;
            this.generateCylinderVertices(vertices, 0, 0, innerRadius, height - wallThickness, segments);
            this.addHollowCylinderFaces(faces, segments, innerStartIndex);
        } else {
            this.addSolidCylinderFaces(faces, segments);
        }
        
        return {
            vertices: vertices,
            faces: faces,
            volume: this.calculateCylinderVolume(params)
        };
    }
    
    generatePrismFrontend(params) {
        const { length: sideLength, height, wallThickness, sides } = params;
        
        // Calculate circumradius for regular polygon
        const radius = sideLength / (2 * Math.sin(Math.PI / sides));
        
        const vertices = [];
        const faces = [];
        
        // Generate outer prism
        this.generatePrismVertices(vertices, 0, 0, radius, height, sides);
        
        // Generate inner prism if hollow
        const innerRadius = radius - wallThickness;
        if (innerRadius > 0) {
            const innerStartIndex = vertices.length;
            this.generatePrismVertices(vertices, 0, 0, innerRadius, height - wallThickness, sides);
            this.addHollowPrismFaces(faces, sides, innerStartIndex);
        } else {
            this.addSolidPrismFaces(faces, sides);
        }
        
        return {
            vertices: vertices,
            faces: faces,
            volume: this.calculatePrismVolume(params)
        };
    }
    
    generateCylinderVertices(vertices, centerX, centerY, radius, height, segments) {
        // Bottom center
        vertices.push({ x: centerX, y: centerY, z: 0 });
        
        // Bottom circle
        for (let i = 0; i < segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            vertices.push({
                x: centerX + Math.cos(angle) * radius,
                y: centerY + Math.sin(angle) * radius,
                z: 0
            });
        }
        
        // Top center
        vertices.push({ x: centerX, y: centerY, z: height });
        
        // Top circle
        for (let i = 0; i < segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            vertices.push({
                x: centerX + Math.cos(angle) * radius,
                y: centerY + Math.sin(angle) * radius,
                z: height
            });
        }
    }
    
    generatePrismVertices(vertices, centerX, centerY, radius, height, sides) {
        // Bottom center
        vertices.push({ x: centerX, y: centerY, z: 0 });
        
        // Bottom polygon
        for (let i = 0; i < sides; i++) {
            const angle = (i / sides) * Math.PI * 2;
            vertices.push({
                x: centerX + Math.cos(angle) * radius,
                y: centerY + Math.sin(angle) * radius,
                z: 0
            });
        }
        
        // Top center
        vertices.push({ x: centerX, y: centerY, z: height });
        
        // Top polygon
        for (let i = 0; i < sides; i++) {
            const angle = (i / sides) * Math.PI * 2;
            vertices.push({
                x: centerX + Math.cos(angle) * radius,
                y: centerY + Math.sin(angle) * radius,
                z: height
            });
        }
    }
    
    addSolidBoxFaces(faces) {
        // Simple box faces (12 triangles)
        const boxFaces = [
            // Bottom
            [0, 1, 2], [0, 2, 3],
            // Top
            [4, 6, 5], [4, 7, 6],
            // Front
            [0, 4, 5], [0, 5, 1],
            // Back
            [2, 6, 7], [2, 7, 3],
            // Left
            [0, 3, 7], [0, 7, 4],
            // Right
            [1, 5, 6], [1, 6, 2]
        ];
        
        faces.push(...boxFaces);
    }
    
    addHollowBoxFaces(faces) {
        // More complex hollow box faces
        // This is a simplified version - full implementation would be more complex
        this.addSolidBoxFaces(faces);
    }
    
    addSolidCylinderFaces(faces, segments) {
        // Bottom faces
        for (let i = 0; i < segments; i++) {
            const next = (i + 1) % segments;
            faces.push([0, i + 1, next + 1]);
        }
        
        // Top faces
        const topCenter = segments + 1;
        const topStart = segments + 2;
        for (let i = 0; i < segments; i++) {
            const next = (i + 1) % segments;
            faces.push([topCenter, topStart + next, topStart + i]);
        }
        
        // Side faces
        for (let i = 0; i < segments; i++) {
            const next = (i + 1) % segments;
            const bottom1 = i + 1;
            const bottom2 = next + 1;
            const top1 = topStart + i;
            const top2 = topStart + next;
            
            faces.push([bottom1, top1, top2]);
            faces.push([bottom1, top2, bottom2]);
        }
    }
    
    addHollowCylinderFaces(faces, segments, innerStartIndex) {
        // Simplified hollow cylinder faces
        this.addSolidCylinderFaces(faces, segments);
    }
    
    addSolidPrismFaces(faces, sides) {
        // Similar to cylinder but with fewer segments
        this.addSolidCylinderFaces(faces, sides);
    }
    
    addHollowPrismFaces(faces, sides, innerStartIndex) {
        // Simplified hollow prism faces
        this.addSolidPrismFaces(faces, sides);
    }
    
    calculateBoxVolume(params) {
        const { length, width, height, wallThickness } = params;
        const outerVolume = length * width * height;
        const innerLength = Math.max(0, length - 2 * wallThickness);
        const innerWidth = Math.max(0, width - 2 * wallThickness);
        const innerHeight = Math.max(0, height - wallThickness);
        const innerVolume = innerLength * innerWidth * innerHeight;
        return (outerVolume - innerVolume) / 1000; // Convert to cm³
    }
    
    calculateCylinderVolume(params) {
        const { length: diameter, height, wallThickness } = params;
        const radius = diameter / 2;
        const outerVolume = Math.PI * radius * radius * height;
        const innerRadius = Math.max(0, radius - wallThickness);
        const innerVolume = Math.PI * innerRadius * innerRadius * Math.max(0, height - wallThickness);
        return (outerVolume - innerVolume) / 1000; // Convert to cm³
    }
    
    calculatePrismVolume(params) {
        const { length: sideLength, height, wallThickness, sides } = params;
        const radius = sideLength / (2 * Math.sin(Math.PI / sides));
        const area = 0.5 * sides * radius * radius * Math.sin(2 * Math.PI / sides);
        const outerVolume = area * height;
        const innerRadius = Math.max(0, radius - wallThickness);
        const innerArea = 0.5 * sides * innerRadius * innerRadius * Math.sin(2 * Math.PI / sides);
        const innerVolume = innerArea * Math.max(0, height - wallThickness);
        return (outerVolume - innerVolume) / 1000; // Convert to cm³
    }
    
    parseMeshData(data) {
        // Parse mesh data from backend
        if (data.vertices && data.faces) {
            return {
                vertices: data.vertices,
                faces: data.faces,
                volume: data.volume || 0
            };
        }
        throw new Error('Invalid mesh data from backend');
    }
    
    exportSTL(mesh) {
        // Simple STL export
        let stl = 'solid container\n';
        
        for (const face of mesh.faces) {
            if (face.length >= 3) {
                const v1 = mesh.vertices[face[0]];
                const v2 = mesh.vertices[face[1]];
                const v3 = mesh.vertices[face[2]];
                
                // Calculate normal (simplified)
                const normal = { x: 0, y: 0, z: 1 };
                
                stl += `  facet normal ${normal.x} ${normal.y} ${normal.z}\n`;
                stl += '    outer loop\n';
                stl += `      vertex ${v1.x} ${v1.y} ${v1.z}\n`;
                stl += `      vertex ${v2.x} ${v2.y} ${v2.z}\n`;
                stl += `      vertex ${v3.x} ${v3.y} ${v3.z}\n`;
                stl += '    endloop\n';
                stl += '  endfacet\n';
            }
        }
        
        stl += 'endsolid container\n';
        return stl;
    }
    
    exportOBJ(mesh) {
        // Simple OBJ export
        let obj = '# Container Generator OBJ Export\n';
        
        // Vertices
        for (const vertex of mesh.vertices) {
            obj += `v ${vertex.x} ${vertex.y} ${vertex.z}\n`;
        }
        
        // Faces (OBJ uses 1-based indexing)
        for (const face of mesh.faces) {
            if (face.length >= 3) {
                obj += `f ${face[0] + 1} ${face[1] + 1} ${face[2] + 1}\n`;
            }
        }
        
        return obj;
    }
}
