@echo off
REM Simple frontend-only launcher for Windows

echo.
echo 🎯 Bambu Studio Style 3D Container Generator - Frontend Only
echo =========================================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. 
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Choose the LTS version for best compatibility.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found: 
node --version

echo.
echo 🔧 Setting up frontend...

cd frontend

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
)

echo.
echo 🚀 Starting development server...
echo.
echo 💡 The application will open automatically in your browser
echo    If not, navigate to: http://localhost:8080
echo.
echo 🎮 Features available in frontend-only mode:
echo    ✅ Professional Bambu Studio interface
echo    ✅ Three.js 3D visualization
echo    ✅ Interactive 3D controls
echo    ✅ Shape generation (Box, Cylinder, Prism)
echo    ✅ Parameter controls
echo    ✅ STL export
echo.
echo 📝 Note: Using JavaScript geometry generation
echo    (C++ backend provides enhanced performance when available)
echo.

REM Start the server
npm start

echo.
echo 👋 Server stopped. Press any key to exit.
pause
