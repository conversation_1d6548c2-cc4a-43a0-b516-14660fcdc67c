#!/usr/bin/env python3
"""Simple test to isolate the issue."""

import sys
print("Starting test...")

try:
    print("1. Importing trimesh...")
    import trimesh
    print("   ✓ Trimesh imported")
    
    print("2. Creating simple box...")
    box = trimesh.creation.box(extents=[100, 60, 40])
    print(f"   ✓ Box created: {len(box.vertices)} vertices")
    
    print("3. Creating cavity...")
    cavity = trimesh.creation.box(extents=[94, 54, 37])
    cavity.apply_translation([3, 3, 3])
    print(f"   ✓ Cavity created: {len(cavity.vertices)} vertices")
    
    print("4. Performing boolean difference...")
    result = box.difference(cavity)
    print(f"   ✓ Boolean difference completed: {len(result.vertices)} vertices")
    
    print("5. Testing ShapeGenerator...")
    from kotak_generator import ShapeGenerator
    print("   ✓ ShapeGenerator imported")
    
    print("6. Creating simple container...")
    mesh = ShapeGenerator.create_rounded_box(100, 60, 40, 3, 0)
    print(f"   ✓ Simple container: {len(mesh.vertices)} vertices")
    
    print("7. Creating rounded container...")
    mesh2 = ShapeGenerator.create_rounded_box(100, 60, 40, 3, 5)
    print(f"   ✓ Rounded container: {len(mesh2.vertices)} vertices")
    
    print("\n✅ All tests passed!")
    
except Exception as e:
    print(f"\n❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
