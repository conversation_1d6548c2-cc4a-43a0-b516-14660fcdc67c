import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import numpy as np
from kotak_generator import ShapeGenerator, simpan_stl
import threading

class Enhanced3DApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced 3D Container Generator")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Current mesh for preview and export
        self.current_mesh = None
        
        # Create main layout
        self.setup_ui()
        
        # Set default values
        self.set_default_values()
        
        # Auto-preview on startup
        self.update_preview()
    
    def setup_ui(self):
        """Setup the main user interface."""
        # Create main paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for controls
        self.setup_control_panel(main_paned)
        
        # Right panel for 3D preview
        self.setup_preview_panel(main_paned)
    
    def setup_control_panel(self, parent):
        """Setup the left control panel."""
        control_frame = ttk.Frame(parent)
        parent.add(control_frame, weight=1)
        
        # Create notebook for different shape types
        self.notebook = ttk.Notebook(control_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Rectangular tab
        self.setup_rectangular_tab()
        
        # Triangular tab
        self.setup_triangular_tab()
        
        # Circular tab
        self.setup_circular_tab()
        
        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
        
        # Action buttons
        self.setup_action_buttons(control_frame)
    
    def setup_rectangular_tab(self):
        """Setup rectangular container controls."""
        rect_frame = ttk.Frame(self.notebook)
        self.notebook.add(rect_frame, text="Rectangular")
        
        # Dimensions group
        dims_group = ttk.LabelFrame(rect_frame, text="Dimensions (mm)")
        dims_group.pack(fill=tk.X, padx=5, pady=5)
        
        # Length
        ttk.Label(dims_group, text="Length:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.rect_length = ttk.Entry(dims_group, width=10)
        self.rect_length.grid(row=0, column=1, padx=5, pady=2)
        self.rect_length.bind('<KeyRelease>', self.on_parameter_change)
        
        # Width
        ttk.Label(dims_group, text="Width:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.rect_width = ttk.Entry(dims_group, width=10)
        self.rect_width.grid(row=1, column=1, padx=5, pady=2)
        self.rect_width.bind('<KeyRelease>', self.on_parameter_change)
        
        # Height
        ttk.Label(dims_group, text="Height:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.rect_height = ttk.Entry(dims_group, width=10)
        self.rect_height.grid(row=2, column=1, padx=5, pady=2)
        self.rect_height.bind('<KeyRelease>', self.on_parameter_change)
        
        # Wall thickness
        ttk.Label(dims_group, text="Wall Thickness:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.rect_thickness = ttk.Entry(dims_group, width=10)
        self.rect_thickness.grid(row=3, column=1, padx=5, pady=2)
        self.rect_thickness.bind('<KeyRelease>', self.on_parameter_change)
        
        # Corner rounding group
        corner_group = ttk.LabelFrame(rect_frame, text="Corner Rounding")
        corner_group.pack(fill=tk.X, padx=5, pady=5)
        
        # Sync corners checkbox
        self.sync_corners = tk.BooleanVar(value=True)
        sync_cb = ttk.Checkbutton(corner_group, text="Sync all corners", variable=self.sync_corners,
                                 command=self.on_sync_corners_change)
        sync_cb.grid(row=0, column=0, columnspan=2, sticky="w", padx=5, pady=2)
        
        # Corner radius controls
        self.setup_corner_controls(corner_group)
    
    def setup_corner_controls(self, parent):
        """Setup corner radius controls."""
        # Main radius (when synced)
        ttk.Label(parent, text="Corner Radius:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.main_corner_radius = ttk.Entry(parent, width=10)
        self.main_corner_radius.grid(row=1, column=1, padx=5, pady=2)
        self.main_corner_radius.bind('<KeyRelease>', self.on_corner_radius_change)
        
        # Individual corner controls (initially disabled)
        individual_frame = ttk.Frame(parent)
        individual_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # Front left
        ttk.Label(individual_frame, text="Front Left:").grid(row=0, column=0, sticky="w")
        self.corner_fl = ttk.Entry(individual_frame, width=8, state="disabled")
        self.corner_fl.grid(row=0, column=1, padx=2)
        self.corner_fl.bind('<KeyRelease>', self.on_parameter_change)
        
        # Front right
        ttk.Label(individual_frame, text="Front Right:").grid(row=0, column=2, sticky="w")
        self.corner_fr = ttk.Entry(individual_frame, width=8, state="disabled")
        self.corner_fr.grid(row=0, column=3, padx=2)
        self.corner_fr.bind('<KeyRelease>', self.on_parameter_change)
        
        # Back left
        ttk.Label(individual_frame, text="Back Left:").grid(row=1, column=0, sticky="w")
        self.corner_bl = ttk.Entry(individual_frame, width=8, state="disabled")
        self.corner_bl.grid(row=1, column=1, padx=2)
        self.corner_bl.bind('<KeyRelease>', self.on_parameter_change)
        
        # Back right
        ttk.Label(individual_frame, text="Back Right:").grid(row=1, column=2, sticky="w")
        self.corner_br = ttk.Entry(individual_frame, width=8, state="disabled")
        self.corner_br.grid(row=1, column=3, padx=2)
        self.corner_br.bind('<KeyRelease>', self.on_parameter_change)
        
        self.individual_corners = [self.corner_fl, self.corner_fr, self.corner_br, self.corner_bl]
    
    def setup_triangular_tab(self):
        """Setup triangular container controls."""
        tri_frame = ttk.Frame(self.notebook)
        self.notebook.add(tri_frame, text="Triangular")
        
        # Dimensions group
        dims_group = ttk.LabelFrame(tri_frame, text="Dimensions (mm)")
        dims_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(dims_group, text="Side Length:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.tri_side = ttk.Entry(dims_group, width=10)
        self.tri_side.grid(row=0, column=1, padx=5, pady=2)
        self.tri_side.bind('<KeyRelease>', self.on_parameter_change)
        
        ttk.Label(dims_group, text="Height:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.tri_height = ttk.Entry(dims_group, width=10)
        self.tri_height.grid(row=1, column=1, padx=5, pady=2)
        self.tri_height.bind('<KeyRelease>', self.on_parameter_change)
        
        ttk.Label(dims_group, text="Wall Thickness:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.tri_thickness = ttk.Entry(dims_group, width=10)
        self.tri_thickness.grid(row=2, column=1, padx=5, pady=2)
        self.tri_thickness.bind('<KeyRelease>', self.on_parameter_change)
        
        # Corner rounding
        corner_group = ttk.LabelFrame(tri_frame, text="Corner Rounding")
        corner_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(corner_group, text="Corner Radius:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.tri_corner_radius = ttk.Entry(corner_group, width=10)
        self.tri_corner_radius.grid(row=0, column=1, padx=5, pady=2)
        self.tri_corner_radius.bind('<KeyRelease>', self.on_parameter_change)
    
    def setup_circular_tab(self):
        """Setup circular container controls."""
        circ_frame = ttk.Frame(self.notebook)
        self.notebook.add(circ_frame, text="Circular")
        
        # Dimensions group
        dims_group = ttk.LabelFrame(circ_frame, text="Dimensions (mm)")
        dims_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(dims_group, text="Diameter:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.circ_diameter = ttk.Entry(dims_group, width=10)
        self.circ_diameter.grid(row=0, column=1, padx=5, pady=2)
        self.circ_diameter.bind('<KeyRelease>', self.on_parameter_change)
        
        ttk.Label(dims_group, text="Height:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.circ_height = ttk.Entry(dims_group, width=10)
        self.circ_height.grid(row=1, column=1, padx=5, pady=2)
        self.circ_height.bind('<KeyRelease>', self.on_parameter_change)
        
        ttk.Label(dims_group, text="Wall Thickness:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.circ_thickness = ttk.Entry(dims_group, width=10)
        self.circ_thickness.grid(row=2, column=1, padx=5, pady=2)
        self.circ_thickness.bind('<KeyRelease>', self.on_parameter_change)
        
        # Quality settings
        quality_group = ttk.LabelFrame(circ_frame, text="Quality")
        quality_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(quality_group, text="Segments:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.circ_segments = ttk.Entry(quality_group, width=10)
        self.circ_segments.grid(row=0, column=1, padx=5, pady=2)
        self.circ_segments.bind('<KeyRelease>', self.on_parameter_change)
    
    def setup_action_buttons(self, parent):
        """Setup action buttons."""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # Export STL button
        export_btn = ttk.Button(button_frame, text="Export STL", command=self.export_stl)
        export_btn.pack(side=tk.LEFT, padx=5)
        
        # Refresh button
        refresh_btn = ttk.Button(button_frame, text="Refresh Preview", command=self.update_preview)
        refresh_btn.pack(side=tk.LEFT, padx=5)
    
    def setup_preview_panel(self, parent):
        """Setup the 3D preview panel."""
        preview_frame = ttk.Frame(parent)
        parent.add(preview_frame, weight=2)
        
        # Create matplotlib figure
        self.fig = plt.Figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, preview_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add toolbar
        toolbar_frame = ttk.Frame(preview_frame)
        toolbar_frame.pack(fill=tk.X)
        
        # Status label
        self.status_label = ttk.Label(toolbar_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)

    def set_default_values(self):
        """Set default values for all controls."""
        # Rectangular defaults
        self.rect_length.insert(0, "100")
        self.rect_width.insert(0, "60")
        self.rect_height.insert(0, "40")
        self.rect_thickness.insert(0, "3")
        self.main_corner_radius.insert(0, "0")

        # Triangular defaults
        self.tri_side.insert(0, "80")
        self.tri_height.insert(0, "40")
        self.tri_thickness.insert(0, "3")
        self.tri_corner_radius.insert(0, "0")

        # Circular defaults
        self.circ_diameter.insert(0, "80")
        self.circ_height.insert(0, "40")
        self.circ_thickness.insert(0, "3")
        self.circ_segments.insert(0, "32")

    def on_tab_changed(self, event):
        """Handle tab change event."""
        self.update_preview()

    def on_parameter_change(self, event=None):
        """Handle parameter change with debouncing."""
        # Cancel previous update if pending
        if hasattr(self, '_update_timer'):
            self.root.after_cancel(self._update_timer)

        # Schedule update after 500ms delay
        self._update_timer = self.root.after(500, self.update_preview)

    def on_sync_corners_change(self):
        """Handle sync corners checkbox change."""
        if self.sync_corners.get():
            # Enable main radius, disable individual
            self.main_corner_radius.config(state="normal")
            for corner in self.individual_corners:
                corner.config(state="disabled")

            # Sync all corners to main radius
            self.sync_corner_values()
        else:
            # Disable main radius, enable individual
            self.main_corner_radius.config(state="disabled")
            for corner in self.individual_corners:
                corner.config(state="normal")

            # Copy main radius to all individual corners
            main_value = self.main_corner_radius.get()
            for corner in self.individual_corners:
                corner.delete(0, tk.END)
                corner.insert(0, main_value)

        self.update_preview()

    def on_corner_radius_change(self, event=None):
        """Handle main corner radius change."""
        if self.sync_corners.get():
            self.sync_corner_values()
        self.on_parameter_change()

    def sync_corner_values(self):
        """Sync all corner values to main radius."""
        main_value = self.main_corner_radius.get()
        for corner in self.individual_corners:
            corner.config(state="normal")
            corner.delete(0, tk.END)
            corner.insert(0, main_value)
            corner.config(state="disabled")

    def get_current_parameters(self):
        """Get parameters for the currently selected shape."""
        current_tab = self.notebook.index(self.notebook.select())

        try:
            if current_tab == 0:  # Rectangular
                length = float(self.rect_length.get())
                width = float(self.rect_width.get())
                height = float(self.rect_height.get())
                thickness = float(self.rect_thickness.get())

                if self.sync_corners.get():
                    corner_radius = float(self.main_corner_radius.get())
                    return ('rectangular', length, width, height, thickness, corner_radius, None)
                else:
                    corners = tuple(float(corner.get()) for corner in self.individual_corners)
                    return ('rectangular', length, width, height, thickness, 0, corners)

            elif current_tab == 1:  # Triangular
                side = float(self.tri_side.get())
                height = float(self.tri_height.get())
                thickness = float(self.tri_thickness.get())
                corner_radius = float(self.tri_corner_radius.get())
                return ('triangular', side, height, thickness, corner_radius)

            elif current_tab == 2:  # Circular
                diameter = float(self.circ_diameter.get())
                height = float(self.circ_height.get())
                thickness = float(self.circ_thickness.get())
                segments = int(self.circ_segments.get())
                return ('circular', diameter, height, thickness, segments)

        except ValueError as e:
            raise ValueError("Please enter valid numeric values for all parameters.")

    def generate_mesh(self):
        """Generate mesh based on current parameters."""
        params = self.get_current_parameters()
        shape_type = params[0]

        if shape_type == 'rectangular':
            _, length, width, height, thickness, corner_radius, individual_corners = params
            return ShapeGenerator.create_rounded_box(
                length, width, height, thickness, corner_radius, individual_corners
            )
        elif shape_type == 'triangular':
            _, side, height, thickness, corner_radius = params
            return ShapeGenerator.create_triangular_container(
                side, height, thickness, corner_radius
            )
        elif shape_type == 'circular':
            _, diameter, height, thickness, segments = params
            return ShapeGenerator.create_circular_container(
                diameter, height, thickness, segments
            )

    def update_preview(self):
        """Update the 3D preview."""
        try:
            self.status_label.config(text="Generating preview...")
            self.root.update()

            # Generate mesh in background thread
            def generate_and_display():
                try:
                    mesh = self.generate_mesh()
                    self.current_mesh = mesh

                    # Update display in main thread
                    self.root.after(0, lambda: self.display_mesh(mesh))

                except Exception as e:
                    self.root.after(0, lambda: self.show_error(str(e)))

            # Run in background thread
            thread = threading.Thread(target=generate_and_display)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.show_error(str(e))

    def display_mesh(self, mesh):
        """Display mesh in the 3D preview."""
        try:
            # Clear previous plot
            self.ax.clear()

            # Convert mesh to displayable format
            vertices = mesh.vertices
            faces = mesh.faces

            # Create 3D polygon collection
            poly3d = [[vertices[face] for face in faces]]
            collection = Poly3DCollection(poly3d, alpha=0.7, facecolor='lightblue', edgecolor='black')
            self.ax.add_collection3d(collection)

            # Set axis limits
            self.ax.set_xlim([vertices[:, 0].min(), vertices[:, 0].max()])
            self.ax.set_ylim([vertices[:, 1].min(), vertices[:, 1].max()])
            self.ax.set_zlim([vertices[:, 2].min(), vertices[:, 2].max()])

            # Set labels
            self.ax.set_xlabel('X (mm)')
            self.ax.set_ylabel('Y (mm)')
            self.ax.set_zlabel('Z (mm)')

            # Set title
            current_tab = self.notebook.index(self.notebook.select())
            shape_names = ['Rectangular', 'Triangular', 'Circular']
            self.ax.set_title(f'{shape_names[current_tab]} Container Preview')

            # Refresh canvas
            self.canvas.draw()

            self.status_label.config(text=f"Preview updated - {len(faces)} faces")

        except Exception as e:
            self.show_error(f"Display error: {str(e)}")

    def show_error(self, message):
        """Show error message."""
        self.status_label.config(text=f"Error: {message}")
        messagebox.showerror("Error", message)

    def export_stl(self):
        """Export current mesh to STL file."""
        if self.current_mesh is None:
            messagebox.showwarning("Warning", "No mesh to export. Please generate a preview first.")
            return

        try:
            filepath = filedialog.asksaveasfilename(
                defaultextension=".stl",
                filetypes=[("STL files", "*.stl")],
                title="Export STL File"
            )

            if filepath:
                simpan_stl(self.current_mesh, filepath)
                messagebox.showinfo("Success", f"STL file exported successfully:\n{filepath}")

        except Exception as e:
            messagebox.showerror("Export Error", str(e))

def main():
    """Main application entry point."""
    root = tk.Tk()
    app = Enhanced3DApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
