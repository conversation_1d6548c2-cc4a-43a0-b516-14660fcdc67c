/* Bambu Studio Theme Specific Styles */

/* Override default theme with Bambu Lab Studio colors */
:root {
  /* Bambu Studio specific colors */
  --bambu-orange: #FF6B35;
  --bambu-blue: #4A90E2;
  --bambu-green: #7ED321;
  --bambu-dark: #1a1a1a;
  --bambu-darker: #0f0f0f;
  --bambu-gray: #2b2b2b;
  --bambu-light-gray: #3a3a3a;
  
  /* Update accent colors to match Bambu Studio */
  --accent-primary: var(--bambu-blue);
  --accent-secondary: #5BA0F2;
  --accent-success: var(--bambu-green);
  --accent-warning: var(--bambu-orange);
}

/* Bambu Studio specific styling */
.app-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--bambu-dark) 0%, var(--bambu-darker) 100%);
}

/* Toolbar Bambu styling */
.toolbar {
  background: linear-gradient(90deg, var(--bambu-gray) 0%, var(--bambu-light-gray) 100%);
  border-bottom: 1px solid var(--bambu-orange);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.logo {
  background: linear-gradient(45deg, var(--bambu-orange), var(--bambu-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.logo i {
  background: linear-gradient(45deg, var(--bambu-orange), var(--bambu-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Panel styling */
.left-panel,
.right-panel {
  background: linear-gradient(180deg, var(--bambu-gray) 0%, var(--bambu-dark) 100%);
  border-color: var(--bambu-light-gray);
}

.panel-section {
  border-bottom-color: var(--bambu-light-gray);
}

.panel-title {
  color: var(--bambu-orange);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Shape buttons Bambu styling */
.shape-btn {
  background: linear-gradient(135deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.shape-btn:hover {
  background: linear-gradient(135deg, var(--bg-hover) 0%, var(--bambu-light-gray) 100%);
  border-color: var(--bambu-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.shape-btn.active {
  background: linear-gradient(135deg, var(--bambu-blue) 0%, var(--bambu-orange) 100%);
  border-color: var(--bambu-orange);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

/* Tool buttons */
.tool-btn {
  background: linear-gradient(135deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
  border: 1px solid var(--bambu-light-gray);
}

.tool-btn:hover {
  background: linear-gradient(135deg, var(--bambu-blue) 0%, var(--bambu-orange) 100%);
  border-color: var(--bambu-orange);
  transform: translateX(2px);
}

/* View controls */
.view-controls {
  background: linear-gradient(135deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
  border: 1px solid var(--bambu-light-gray);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.view-btn.active {
  background: linear-gradient(135deg, var(--bambu-orange) 0%, var(--bambu-blue) 100%);
  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.4);
}

/* Viewport */
.viewport-header {
  background: linear-gradient(90deg, var(--bambu-gray) 0%, var(--bambu-light-gray) 100%);
  border-bottom-color: var(--bambu-orange);
}

.viewport-container {
  background: var(--bambu-darker);
  border: 2px solid var(--bambu-light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.viewport-canvas {
  background: radial-gradient(circle at center, var(--bambu-darker) 0%, #000000 100%);
}

/* Tabs */
.panel-tabs {
  background: linear-gradient(90deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
}

.tab-btn.active {
  background: linear-gradient(180deg, var(--bambu-gray) 0%, var(--bambu-dark) 100%);
  border-bottom-color: var(--bambu-orange);
  box-shadow: 0 -2px 6px rgba(255, 107, 53, 0.3);
}

/* Form controls */
input[type="number"],
input[type="text"],
select {
  background: linear-gradient(135deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
  border-color: var(--bambu-light-gray);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

input[type="number"]:focus,
input[type="text"]:focus,
select:focus {
  border-color: var(--bambu-blue);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

/* Export buttons */
.export-btn.primary {
  background: linear-gradient(135deg, var(--bambu-blue) 0%, var(--bambu-orange) 100%);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
  border: none;
}

.export-btn.primary:hover {
  background: linear-gradient(135deg, var(--bambu-orange) 0%, var(--bambu-blue) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
}

.export-btn.secondary {
  background: linear-gradient(135deg, var(--bambu-light-gray) 0%, var(--bambu-gray) 100%);
  border-color: var(--bambu-light-gray);
}

.export-btn.secondary:hover {
  background: linear-gradient(135deg, var(--bambu-gray) 0%, var(--bambu-light-gray) 100%);
  border-color: var(--bambu-blue);
}

/* Status indicator */
.status-dot.success {
  background: radial-gradient(circle, var(--bambu-green) 0%, #5BA321 100%);
  box-shadow: 0 0 6px rgba(126, 211, 33, 0.6);
}

.status-dot.warning {
  background: radial-gradient(circle, var(--bambu-orange) 0%, #CC4A1F 100%);
  box-shadow: 0 0 6px rgba(255, 107, 53, 0.6);
}

.status-dot.error {
  background: radial-gradient(circle, var(--accent-error) 0%, #A0011A 100%);
  box-shadow: 0 0 6px rgba(208, 2, 27, 0.6);
}

.status-dot.loading {
  background: radial-gradient(circle, var(--bambu-blue) 0%, #3A7BC8 100%);
  box-shadow: 0 0 6px rgba(74, 144, 226, 0.6);
}

/* Loading spinner */
.loading-spinner {
  background: rgba(15, 15, 15, 0.8);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--bambu-light-gray);
}

.loading-spinner i {
  background: linear-gradient(45deg, var(--bambu-orange), var(--bambu-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Model info */
.info-value {
  color: var(--bambu-blue);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Scrollbar theming */
.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--bambu-orange) 0%, var(--bambu-blue) 100%);
  border-radius: 3px;
}

/* Hover effects */
.toolbar-btn:hover,
.viewport-btn:hover {
  background: linear-gradient(135deg, var(--bambu-blue) 0%, var(--bambu-orange) 100%);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
}

/* Property groups */
.group-title {
  color: var(--bambu-orange);
  border-bottom-color: var(--bambu-light-gray);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .logo {
    font-size: var(--font-size-base);
  }
  
  .shape-btn,
  .tool-btn {
    padding: var(--spacing-xs);
  }
  
  .export-btn {
    height: 36px;
    font-size: var(--font-size-xs);
  }
}

/* Animation enhancements */
.shape-btn,
.tool-btn,
.export-btn,
.toolbar-btn,
.viewport-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
.shape-btn:focus-visible,
.tool-btn:focus-visible,
.export-btn:focus-visible {
  outline: 2px solid var(--bambu-orange);
  outline-offset: 2px;
}
