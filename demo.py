#!/usr/bin/env python3
"""
Demo script showcasing the Enhanced 3D Container Generator features.
This script generates sample containers and exports them as STL files.
"""

from kotak_generator import ShapeGenerator, simpan_stl
import os

def create_demo_containers():
    """Create demonstration containers showcasing different features."""
    
    print("🎯 Enhanced 3D Container Generator - Demo")
    print("=" * 50)
    
    # Create output directory
    output_dir = "demo_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 Created output directory: {output_dir}")
    
    demos = []
    
    # 1. Basic rectangular container
    print("\n1️⃣  Creating basic rectangular container...")
    try:
        basic_rect = ShapeGenerator.create_rounded_box(100, 60, 40, 3)
        filename = os.path.join(output_dir, "01_basic_rectangular.stl")
        simpan_stl(basic_rect, filename)
        demos.append(("Basic Rectangular Container", filename, "100x60x40mm, 3mm walls"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 2. Rounded rectangular container
    print("\n2️⃣  Creating rounded rectangular container...")
    try:
        rounded_rect = ShapeGenerator.create_rounded_box(100, 60, 40, 3, corner_radius=8)
        filename = os.path.join(output_dir, "02_rounded_rectangular.stl")
        simpan_stl(rounded_rect, filename)
        demos.append(("Rounded Rectangular Container", filename, "100x60x40mm, 3mm walls, 8mm corner radius"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 3. Individual corner radii
    print("\n3️⃣  Creating container with individual corner radii...")
    try:
        individual_corners = ShapeGenerator.create_rounded_box(
            120, 80, 50, 4, 0, (2, 6, 10, 14)
        )
        filename = os.path.join(output_dir, "03_individual_corners.stl")
        simpan_stl(individual_corners, filename)
        demos.append(("Individual Corner Radii", filename, "120x80x50mm, 4mm walls, corners: 2,6,10,14mm"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 4. Triangular container
    print("\n4️⃣  Creating triangular container...")
    try:
        triangular = ShapeGenerator.create_triangular_container(80, 40, 3, corner_radius=2)
        filename = os.path.join(output_dir, "04_triangular.stl")
        simpan_stl(triangular, filename)
        demos.append(("Triangular Container", filename, "80mm sides, 40mm height, 3mm walls, 2mm corners"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 5. Small circular container (low resolution)
    print("\n5️⃣  Creating small circular container (low-res)...")
    try:
        small_circular = ShapeGenerator.create_circular_container(60, 30, 2, segments=16)
        filename = os.path.join(output_dir, "05_small_circular_lowres.stl")
        simpan_stl(small_circular, filename)
        demos.append(("Small Circular (Low-res)", filename, "60mm diameter, 30mm height, 2mm walls, 16 segments"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 6. Large circular container (high resolution)
    print("\n6️⃣  Creating large circular container (high-res)...")
    try:
        large_circular = ShapeGenerator.create_circular_container(120, 60, 4, segments=64)
        filename = os.path.join(output_dir, "06_large_circular_highres.stl")
        simpan_stl(large_circular, filename)
        demos.append(("Large Circular (High-res)", filename, "120mm diameter, 60mm height, 4mm walls, 64 segments"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 7. Thin-walled precision container
    print("\n7️⃣  Creating thin-walled precision container...")
    try:
        precision = ShapeGenerator.create_rounded_box(50, 30, 20, 1, corner_radius=3)
        filename = os.path.join(output_dir, "07_precision_thin_walls.stl")
        simpan_stl(precision, filename)
        demos.append(("Precision Thin-walled", filename, "50x30x20mm, 1mm walls, 3mm corners"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # 8. Large storage container
    print("\n8️⃣  Creating large storage container...")
    try:
        storage = ShapeGenerator.create_rounded_box(200, 150, 80, 5, corner_radius=12)
        filename = os.path.join(output_dir, "08_large_storage.stl")
        simpan_stl(storage, filename)
        demos.append(("Large Storage Container", filename, "200x150x80mm, 5mm walls, 12mm corners"))
        print(f"   ✅ Exported: {filename}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Demo Summary")
    print("=" * 50)
    
    for i, (name, filename, specs) in enumerate(demos, 1):
        size = os.path.getsize(filename) if os.path.exists(filename) else 0
        print(f"{i:2d}. {name}")
        print(f"    📄 File: {filename}")
        print(f"    📏 Specs: {specs}")
        print(f"    💾 Size: {size:,} bytes")
        print()
    
    print(f"🎉 Demo completed! {len(demos)} containers generated.")
    print(f"📁 All files saved in: {output_dir}/")
    print("\n💡 Tips:")
    print("   • Import these STL files into your 3D printing software")
    print("   • Adjust print settings based on your printer capabilities")
    print("   • Consider supports for tall containers")
    print("   • Test with small containers first")

def show_parameter_examples():
    """Show examples of parameter ranges for different use cases."""
    print("\n📐 Parameter Guidelines")
    print("=" * 30)
    
    examples = [
        ("Small Parts Storage", "30-60mm", "20-40mm", "1-2mm", "1-3mm"),
        ("Desktop Organizer", "80-120mm", "40-60mm", "2-3mm", "3-6mm"),
        ("Workshop Storage", "150-250mm", "60-100mm", "3-5mm", "5-15mm"),
        ("Precision Tools", "40-80mm", "15-30mm", "0.8-1.5mm", "1-2mm"),
    ]
    
    print(f"{'Use Case':<20} {'Dimensions':<12} {'Height':<10} {'Walls':<8} {'Corners'}")
    print("-" * 65)
    
    for use_case, dims, height, walls, corners in examples:
        print(f"{use_case:<20} {dims:<12} {height:<10} {walls:<8} {corners}")

if __name__ == "__main__":
    create_demo_containers()
    show_parameter_examples()
    
    print("\n🚀 Ready to start designing? Run:")
    print("   python enhanced_app.py")
