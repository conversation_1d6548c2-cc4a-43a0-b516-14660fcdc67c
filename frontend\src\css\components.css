/* Component Styles */

/* Buttons */
.toolbar-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  height: var(--button-height);
  padding: 0 var(--spacing-sm);
  background-color: transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.toolbar-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.toolbar-btn:active {
  background-color: var(--bg-active);
}

.toolbar-btn i {
  font-size: var(--font-size-sm);
}

/* Shape Tools */
.shape-tools {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.shape-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.shape-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.shape-btn.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.shape-btn i {
  font-size: var(--font-size-lg);
  width: 20px;
  text-align: center;
}

/* Edge Tools */
.edge-tools {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.tool-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.tool-btn:active {
  background-color: var(--accent-primary);
}

.tool-btn i {
  font-size: var(--font-size-sm);
  width: 16px;
  text-align: center;
}

/* View Controls */
.view-controls {
  display: flex;
  gap: var(--spacing-xs);
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius);
  padding: var(--spacing-xs);
}

.view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: transparent;
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.view-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.view-btn.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Viewport Controls */
.viewport-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.viewport-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.viewport-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.viewport-btn.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Form Controls */
input[type="number"],
input[type="text"],
select {
  height: var(--input-height);
  padding: 0 var(--spacing-sm);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

input[type="number"]:focus,
input[type="text"]:focus,
select:focus {
  border-color: var(--accent-primary);
  background-color: var(--bg-hover);
}

input[type="color"] {
  width: 40px;
  height: var(--input-height);
  padding: 2px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
}

/* Export Buttons */
.export-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  height: 40px;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.export-btn.primary {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.export-btn.primary:hover {
  background-color: var(--accent-secondary);
}

.export-btn.secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.export-btn.secondary:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.logo i {
  color: var(--accent-primary);
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-muted);
}

.status-dot.success {
  background-color: var(--accent-success);
}

.status-dot.warning {
  background-color: var(--accent-warning);
}

.status-dot.error {
  background-color: var(--accent-error);
}

.status-dot.loading {
  background-color: var(--accent-primary);
  animation: pulse 1.5s ease-in-out infinite;
}

/* Model Info */
.model-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.info-label {
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* Viewport Title */
.viewport-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* Loading Spinner */
.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: none;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  z-index: 100;
}

.loading-spinner i {
  font-size: var(--font-size-2xl);
  color: var(--accent-primary);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* Scrollbars for panels */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.tab-content::-webkit-scrollbar {
  width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
  background-color: var(--bg-tertiary);
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--bg-hover);
}
