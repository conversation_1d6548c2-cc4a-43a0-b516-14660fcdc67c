<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced 3D Container Generator - Bambu Studio Style</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        bambu: {
                            orange: '#FF6B35',
                            blue: '#4A90E2',
                            green: '#7ED321',
                            dark: '#1a1a1a',
                            darker: '#0f0f0f',
                            gray: '#2b2b2b',
                            'light-gray': '#3a3a3a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Three.js with proper importmap -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>
    
    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #2b2b2b;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #4A90E2;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #5BA0F2;
        }
        
        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Glass effect */
        .glass {
            backdrop-filter: blur(10px);
            background: rgba(26, 26, 26, 0.8);
        }
    </style>
</head>
<body class="bg-bambu-darker text-white font-sans overflow-hidden">
    <div class="flex h-screen">
        <!-- Left Sidebar -->
        <div class="w-80 bg-bambu-gray border-r border-bambu-light-gray flex flex-col custom-scrollbar overflow-y-auto">
            <!-- Header -->
            <div class="p-6 border-b border-bambu-light-gray">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-cube text-2xl text-bambu-orange"></i>
                    <div>
                        <h1 class="text-xl font-bold text-white">Container Generator</h1>
                        <p class="text-sm text-gray-400">Advanced 3D Design</p>
                    </div>
                </div>
            </div>
            
            <!-- Shape Configuration -->
            <div class="p-6 space-y-6">
                <!-- Sides Count -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-bambu-blue">
                        <i class="fas fa-shapes mr-2"></i>Shape Sides
                    </label>
                    <div class="relative">
                        <input type="range" id="sidesSlider" min="1" max="12" value="4" 
                               class="w-full h-2 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer slider">
                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                            <span>1 (Cylinder)</span>
                            <span>4 (Box)</span>
                            <span>12 (Dodecagon)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-300">Current Shape:</span>
                        <span id="shapeType" class="text-bambu-orange font-semibold">Box</span>
                    </div>
                </div>
                
                <!-- Dynamic Configuration Panel -->
                <div id="configPanel" class="space-y-4">
                    <!-- Will be populated dynamically -->
                </div>
                
                <!-- Render Options -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-bambu-blue">
                        <i class="fas fa-eye mr-2"></i>Render Mode
                    </label>
                    <div class="grid grid-cols-2 gap-2">
                        <button id="solidMode" class="px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600 active">
                            <i class="fas fa-cube mr-2"></i>Solid
                        </button>
                        <button id="wireframeMode" class="px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-project-diagram mr-2"></i>Wireframe
                        </button>
                    </div>
                </div>
                
                <!-- View Controls -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-bambu-blue">
                        <i class="fas fa-camera mr-2"></i>View Controls
                    </label>
                    <div class="grid grid-cols-2 gap-2">
                        <button id="resetView" class="px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-undo mr-2"></i>Reset
                        </button>
                        <button id="fitView" class="px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-expand-arrows-alt mr-2"></i>Fit
                        </button>
                    </div>
                </div>
                
                <!-- Export Section -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-bambu-blue">
                        <i class="fas fa-download mr-2"></i>Export
                    </label>
                    <button id="exportSTL" class="w-full px-4 py-3 bg-gradient-to-r from-bambu-orange to-bambu-blue text-white rounded-lg font-semibold transition-all hover:shadow-lg hover:scale-105">
                        <i class="fas fa-file-export mr-2"></i>Export STL
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Viewport -->
        <div class="flex-1 relative bg-bambu-darker">
            <!-- Viewport Header -->
            <div class="absolute top-0 left-0 right-0 z-10 p-4">
                <div class="glass rounded-lg p-3 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-lg font-semibold text-white">3D Viewport</h2>
                        <div class="flex items-center space-x-2 text-sm text-gray-300">
                            <i class="fas fa-info-circle"></i>
                            <span id="geometryInfo">Ready</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="gridToggle" class="p-2 bg-bambu-light-gray rounded-lg hover:bg-gray-600 transition-all">
                            <i class="fas fa-th"></i>
                        </button>
                        <button id="axesToggle" class="p-2 bg-bambu-light-gray rounded-lg hover:bg-gray-600 transition-all">
                            <i class="fas fa-arrows-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 3D Canvas -->
            <canvas id="canvas3d" class="w-full h-full"></canvas>
            
            <!-- Status Panel -->
            <div class="absolute bottom-4 left-4 right-4 z-10">
                <div class="glass rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div id="statusIndicator" class="w-3 h-3 bg-bambu-green rounded-full animate-pulse"></div>
                            <span id="statusText" class="text-sm text-gray-300">Initializing...</span>
                        </div>
                        <div class="text-xs text-gray-400">
                            <span id="triangleCount">0 triangles</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Debug Panel (toggleable) -->
            <div id="debugPanel" class="absolute top-20 right-4 w-80 glass rounded-lg p-4 max-h-96 overflow-y-auto custom-scrollbar hidden">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-bambu-orange">Debug Information</h3>
                    <button id="closeDebug" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="debugContent" class="text-xs text-gray-300 space-y-1 font-mono">
                    <!-- Debug messages will appear here -->
                </div>
            </div>
            
            <!-- Debug Toggle -->
            <button id="debugToggle" class="absolute top-4 right-4 p-2 bg-bambu-light-gray rounded-lg hover:bg-gray-600 transition-all z-20">
                <i class="fas fa-bug"></i>
            </button>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        // Global variables
        let scene, camera, renderer, controls;
        let currentMesh = null;
        let currentSides = 4;
        let renderMode = 'solid';
        let showGrid = true;
        let showAxes = true;
        let gridHelper, axesHelper;
        
        // Configuration templates for different shapes
        const shapeConfigs = {
            cylinder: {
                name: 'Cylinder',
                icon: 'fas fa-circle',
                params: [
                    { id: 'diameter', label: 'Diameter (mm)', type: 'number', value: 80, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 50, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 2, min: 0.1, max: 50, step: 0.1 },
                    { id: 'segments', label: 'Quality (segments)', type: 'range', value: 32, min: 8, max: 64 }
                ]
            },
            box: {
                name: 'Box',
                icon: 'fas fa-cube',
                params: [
                    { id: 'length', label: 'Length (mm)', type: 'number', value: 100, min: 1, max: 500 },
                    { id: 'width', label: 'Width (mm)', type: 'number', value: 60, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 40, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 3, min: 0.1, max: 50, step: 0.1 }
                ]
            },
            polygon: {
                name: 'Polygon',
                icon: 'fas fa-shapes',
                params: [
                    { id: 'diameter', label: 'Diameter (mm)', type: 'number', value: 80, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 50, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 2, min: 0.1, max: 50, step: 0.1 }
                ]
            }
        };
        
        // Debug function
        function debug(message) {
            console.log(message);
            const debugContent = document.getElementById('debugContent');
            if (debugContent) {
                const timestamp = new Date().toLocaleTimeString();
                debugContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                debugContent.scrollTop = debugContent.scrollHeight;
            }
        }
        
        // Initialize application
        async function init() {
            try {
                debug('🎯 Starting Advanced 3D Container Generator');
                updateStatus('Initializing Three.js...', 'loading');
                
                await initThreeJS();
                setupEventListeners();
                updateShapeConfig();
                updateGeometry();
                
                updateStatus('Ready - Design your container', 'success');
                debug('✅ Application initialized successfully');
                
            } catch (error) {
                debug('❌ Initialization failed: ' + error.message);
                updateStatus('Initialization failed', 'error');
                console.error('Initialization error:', error);
            }
        }
        
        async function initThreeJS() {
            // Get canvas
            const canvas = document.getElementById('canvas3d');
            if (!canvas) throw new Error('Canvas not found');
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0f0f0f);
            
            // Create camera
            const aspect = canvas.clientWidth / canvas.clientHeight;
            camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
            camera.position.set(150, 100, 150);
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true,
                alpha: true
            });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // Create controls
            controls = new OrbitControls(camera, canvas);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.screenSpacePanning = false;
            controls.minDistance = 10;
            controls.maxDistance = 500;
            
            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Add helpers
            gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
            scene.add(gridHelper);
            
            axesHelper = new THREE.AxesHelper(50);
            scene.add(axesHelper);
            
            // Handle resize
            window.addEventListener('resize', onWindowResize);
            
            // Start render loop
            animate();
            
            debug('✅ Three.js initialized successfully');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }
        
        function onWindowResize() {
            if (!camera || !renderer) return;
            
            const canvas = document.getElementById('canvas3d');
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        }
        
        function setupEventListeners() {
            // Sides slider
            document.getElementById('sidesSlider').addEventListener('input', (e) => {
                currentSides = parseInt(e.target.value);
                updateShapeConfig();
                updateGeometry();
            });

            // Render mode buttons
            document.getElementById('solidMode').addEventListener('click', () => setRenderMode('solid'));
            document.getElementById('wireframeMode').addEventListener('click', () => setRenderMode('wireframe'));

            // View controls
            document.getElementById('resetView').addEventListener('click', resetView);
            document.getElementById('fitView').addEventListener('click', fitView);
            document.getElementById('gridToggle').addEventListener('click', toggleGrid);
            document.getElementById('axesToggle').addEventListener('click', toggleAxes);

            // Export
            document.getElementById('exportSTL').addEventListener('click', exportSTL);

            // Debug panel
            document.getElementById('debugToggle').addEventListener('click', toggleDebug);
            document.getElementById('closeDebug').addEventListener('click', () => {
                document.getElementById('debugPanel').classList.add('hidden');
            });
        }

        function updateShapeConfig() {
            const shapeType = getShapeType(currentSides);
            const config = shapeConfigs[shapeType];

            // Update shape type display
            document.getElementById('shapeType').textContent = config.name;

            // Generate configuration panel
            const configPanel = document.getElementById('configPanel');
            configPanel.innerHTML = `
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 text-sm font-semibold text-bambu-orange">
                        <i class="${config.icon}"></i>
                        <span>${config.name} Configuration</span>
                    </div>
                    ${config.params.map(param => createParamHTML(param)).join('')}
                </div>
            `;

            // Add event listeners to new inputs
            config.params.forEach(param => {
                const element = document.getElementById(param.id);
                if (element) {
                    element.addEventListener('input', updateGeometry);
                }
            });

            debug(`🔧 Updated configuration for ${config.name} (${currentSides} sides)`);
        }

        function createParamHTML(param) {
            const inputClass = param.type === 'range'
                ? 'w-full h-2 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer'
                : 'w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white focus:border-bambu-blue focus:outline-none';

            return `
                <div class="space-y-2">
                    <label class="block text-sm text-gray-300">${param.label}</label>
                    <input type="${param.type}" id="${param.id}"
                           value="${param.value}"
                           min="${param.min || ''}"
                           max="${param.max || ''}"
                           step="${param.step || ''}"
                           class="${inputClass}">
                    ${param.type === 'range' ? `
                        <div class="flex justify-between text-xs text-gray-400">
                            <span>${param.min}</span>
                            <span id="${param.id}Value">${param.value}</span>
                            <span>${param.max}</span>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function getShapeType(sides) {
            if (sides === 1) return 'cylinder';
            if (sides === 4) return 'box';
            return 'polygon';
        }

        function getCurrentParams() {
            const shapeType = getShapeType(currentSides);
            const config = shapeConfigs[shapeType];
            const params = { sides: currentSides };

            config.params.forEach(param => {
                const element = document.getElementById(param.id);
                if (element) {
                    params[param.id] = parseFloat(element.value) || param.value;

                    // Update range display
                    if (param.type === 'range') {
                        const valueDisplay = document.getElementById(param.id + 'Value');
                        if (valueDisplay) {
                            valueDisplay.textContent = element.value;
                        }
                    }
                }
            });

            return params;
        }

        function updateGeometry() {
            try {
                debug('🔧 Generating geometry...');
                updateStatus('Generating geometry...', 'loading');

                // Remove existing mesh
                if (currentMesh) {
                    scene.remove(currentMesh);
                    currentMesh.geometry.dispose();
                    currentMesh.material.dispose();
                    currentMesh = null;
                }

                const params = getCurrentParams();
                let geometry;

                // Generate geometry based on sides
                if (currentSides === 1) {
                    geometry = createHollowCylinderGeometry(
                        params.diameter / 2,
                        params.height,
                        params.wallThickness,
                        params.segments
                    );
                } else if (currentSides === 4) {
                    geometry = createHollowBoxGeometry(
                        params.length,
                        params.width,
                        params.height,
                        params.wallThickness
                    );
                } else {
                    geometry = createHollowCylinderGeometry(
                        params.diameter / 2,
                        params.height,
                        params.wallThickness,
                        currentSides
                    );
                }

                // Create material based on render mode
                const material = createMaterial();

                // Create mesh
                currentMesh = new THREE.Mesh(geometry, material);
                currentMesh.castShadow = true;
                currentMesh.receiveShadow = true;

                // Add to scene
                scene.add(currentMesh);

                const triangleCount = geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3;
                document.getElementById('triangleCount').textContent = `${Math.floor(triangleCount)} triangles`;
                document.getElementById('geometryInfo').textContent = `${getShapeType(currentSides)} - ${Math.floor(triangleCount)} triangles`;

                debug(`✅ Generated ${getShapeType(currentSides)} with ${Math.floor(triangleCount)} triangles`);
                updateStatus('Geometry generated successfully', 'success');

            } catch (error) {
                debug('❌ Geometry generation failed: ' + error.message);
                updateStatus('Generation failed', 'error');
                console.error('Geometry error:', error);
            }
        }

        function createMaterial() {
            const materialProps = {
                color: 0x4A90E2,
                transparent: true,
                opacity: 0.9
            };

            if (renderMode === 'wireframe') {
                return new THREE.MeshBasicMaterial({
                    ...materialProps,
                    wireframe: true,
                    opacity: 1
                });
            } else {
                return new THREE.MeshPhongMaterial({
                    ...materialProps,
                    shininess: 100
                });
            }
        }

        function setRenderMode(mode) {
            renderMode = mode;

            // Update button states
            document.getElementById('solidMode').className = mode === 'solid'
                ? 'px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600 active'
                : 'px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600';

            document.getElementById('wireframeMode').className = mode === 'wireframe'
                ? 'px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600 active'
                : 'px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600';

            // Update existing mesh material
            if (currentMesh) {
                currentMesh.material.dispose();
                currentMesh.material = createMaterial();
            }

            debug(`🎨 Render mode changed to ${mode}`);
        }

        function resetView() {
            if (!camera || !controls) return;
            camera.position.set(150, 100, 150);
            camera.lookAt(0, 0, 0);
            controls.reset();
            debug('🔄 View reset');
            updateStatus('View reset', 'success');
        }

        function fitView() {
            if (!currentMesh || !camera || !controls) return;

            try {
                const box = new THREE.Box3().setFromObject(currentMesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());

                const maxDim = Math.max(size.x, size.y, size.z);
                const distance = maxDim * 2;

                camera.position.copy(center);
                camera.position.x += distance;
                camera.position.y += distance * 0.5;
                camera.position.z += distance;
                camera.lookAt(center);

                controls.target.copy(center);
                controls.update();

                debug('📐 View fitted to object');
                updateStatus('View fitted', 'success');

            } catch (error) {
                debug('❌ Fit view failed: ' + error.message);
            }
        }

        function toggleGrid() {
            showGrid = !showGrid;
            gridHelper.visible = showGrid;
            debug(`🔲 Grid ${showGrid ? 'enabled' : 'disabled'}`);
        }

        function toggleAxes() {
            showAxes = !showAxes;
            axesHelper.visible = showAxes;
            debug(`📐 Axes ${showAxes ? 'enabled' : 'disabled'}`);
        }

        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            panel.classList.toggle('hidden');
        }

        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('statusText');
            const statusIndicator = document.getElementById('statusIndicator');

            statusText.textContent = message;

            // Update indicator color
            statusIndicator.className = 'w-3 h-3 rounded-full ';
            switch (type) {
                case 'success':
                    statusIndicator.className += 'bg-bambu-green';
                    break;
                case 'error':
                    statusIndicator.className += 'bg-red-500 animate-pulse';
                    break;
                case 'loading':
                    statusIndicator.className += 'bg-bambu-orange animate-pulse';
                    break;
                default:
                    statusIndicator.className += 'bg-bambu-blue';
            }
        }

        // Geometry generation functions (reusing from previous implementation)
        function createHollowBoxGeometry(length, width, height, wallThickness) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            const t = Math.min(wallThickness, Math.min(length, width, height) / 4);
            const l = length, w = width, h = height;
            const il = Math.max(0, l - 2 * t);
            const iw = Math.max(0, w - 2 * t);
            const ih = Math.max(0, h - t);

            // Outer vertices
            vertices.push(
                -l/2, 0, -w/2, l/2, 0, -w/2, l/2, 0, w/2, -l/2, 0, w/2,
                -l/2, h, -w/2, l/2, h, -w/2, l/2, h, w/2, -l/2, h, w/2
            );

            if (il > 0 && iw > 0 && ih > 0) {
                vertices.push(
                    -il/2, t, -iw/2, il/2, t, -iw/2, il/2, t, iw/2, -il/2, t, iw/2,
                    -il/2, h, -iw/2, il/2, h, -iw/2, il/2, h, iw/2, -il/2, h, iw/2
                );
            }

            // Create faces
            indices.push(0, 1, 2, 0, 2, 3); // Bottom
            indices.push(0, 4, 5, 0, 5, 1, 1, 5, 6, 1, 6, 2, 2, 6, 7, 2, 7, 3, 3, 7, 4, 3, 4, 0); // Sides

            if (il > 0 && iw > 0 && ih > 0) {
                indices.push(8, 10, 9, 8, 11, 10); // Inner bottom
                indices.push(8, 9, 13, 8, 13, 12, 9, 10, 14, 9, 14, 13, 10, 11, 15, 10, 15, 14, 11, 8, 12, 11, 12, 15); // Inner sides
                indices.push(0, 8, 9, 0, 9, 1, 1, 9, 10, 1, 10, 2, 2, 10, 11, 2, 11, 3, 3, 11, 8, 3, 8, 0); // Bottom walls
                indices.push(4, 12, 13, 4, 13, 5, 5, 13, 14, 5, 14, 6, 6, 14, 15, 6, 15, 7, 7, 15, 12, 7, 12, 4); // Top rim
            } else {
                indices.push(4, 6, 5, 4, 7, 6); // Solid top
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();
            return geometry;
        }

        function createHollowCylinderGeometry(radius, height, wallThickness, segments) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            const t = Math.min(wallThickness, radius / 2);
            const innerRadius = Math.max(0, radius - t);
            const innerHeight = Math.max(0, height - t);

            // Outer cylinder
            vertices.push(0, 0, 0); // Center bottom
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(Math.cos(angle) * radius, 0, Math.sin(angle) * radius);
            }

            vertices.push(0, height, 0); // Center top
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(Math.cos(angle) * radius, height, Math.sin(angle) * radius);
            }

            // Inner cylinder
            if (innerRadius > 0 && innerHeight > 0) {
                vertices.push(0, t, 0); // Inner center bottom
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(Math.cos(angle) * innerRadius, t, Math.sin(angle) * innerRadius);
                }

                vertices.push(0, height, 0); // Inner center top
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(Math.cos(angle) * innerRadius, height, Math.sin(angle) * innerRadius);
                }
            }

            // Create faces
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                indices.push(0, i + 1, next + 1); // Outer bottom
            }

            const outerTopStart = segments + 2;
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                const bottom1 = i + 1, bottom2 = next + 1;
                const top1 = outerTopStart + i, top2 = outerTopStart + next;
                indices.push(bottom1, top1, top2, bottom1, top2, bottom2); // Outer sides
            }

            if (innerRadius > 0 && innerHeight > 0) {
                const innerBottomStart = segments * 2 + 3;
                const innerTopStart = segments * 3 + 4;

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(innerBottomStart, innerBottomStart + next, innerBottomStart + i); // Inner bottom
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const bottom1 = innerBottomStart + i, bottom2 = innerBottomStart + next;
                    const top1 = innerTopStart + i, top2 = innerTopStart + next;
                    indices.push(bottom1, top2, top1, bottom1, bottom2, top2); // Inner sides
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = i + 1, outer2 = next + 1;
                    const inner1 = innerBottomStart + i, inner2 = innerBottomStart + next;
                    indices.push(outer1, inner1, inner2, outer1, inner2, outer2); // Bottom wall
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = outerTopStart + i, outer2 = outerTopStart + next;
                    const inner1 = innerTopStart + i, inner2 = innerTopStart + next;
                    indices.push(outer1, inner2, inner1, outer1, outer2, inner2); // Top rim
                }
            } else {
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(segments + 1, outerTopStart + next, outerTopStart + i); // Solid top
                }
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();
            return geometry;
        }

        function exportSTL() {
            if (!currentMesh) {
                debug('❌ No geometry to export');
                updateStatus('No geometry to export', 'error');
                return;
            }

            try {
                debug('💾 Exporting STL...');
                updateStatus('Exporting STL...', 'loading');

                const geometry = currentMesh.geometry;
                const positionAttribute = geometry.attributes.position;
                const indexAttribute = geometry.index;

                let stl = 'solid container\n';
                let triangleCount = 0;

                if (indexAttribute) {
                    for (let i = 0; i < indexAttribute.count; i += 3) {
                        const i1 = indexAttribute.getX(i);
                        const i2 = indexAttribute.getX(i + 1);
                        const i3 = indexAttribute.getX(i + 2);

                        const v1 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i1);
                        const v2 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i2);
                        const v3 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i3);

                        const normal = new THREE.Vector3();
                        const edge1 = new THREE.Vector3().subVectors(v2, v1);
                        const edge2 = new THREE.Vector3().subVectors(v3, v1);
                        normal.crossVectors(edge1, edge2).normalize();

                        if (normal.length() > 0.001) {
                            stl += `  facet normal ${normal.x.toFixed(6)} ${normal.y.toFixed(6)} ${normal.z.toFixed(6)}\n`;
                            stl += `    outer loop\n`;
                            stl += `      vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                            stl += `      vertex ${v2.x.toFixed(6)} ${v2.y.toFixed(6)} ${v2.z.toFixed(6)}\n`;
                            stl += `      vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                            stl += `    endloop\n`;
                            stl += `  endfacet\n`;
                            triangleCount++;
                        }
                    }
                }

                stl += 'endsolid container\n';

                const blob = new Blob([stl], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${getShapeType(currentSides)}_${currentSides}sides_container.stl`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                debug(`✅ STL exported with ${triangleCount} triangles`);
                updateStatus(`STL exported - ${triangleCount} triangles`, 'success');

            } catch (error) {
                debug('❌ STL export failed: ' + error.message);
                updateStatus('Export failed', 'error');
                console.error('Export error:', error);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', init);
        
    </script>
</body>
</html>
