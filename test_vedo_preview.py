#!/usr/bin/env python3
"""
Test script for vedo 3D preview functionality.
"""

from kotak_generator import <PERSON>hapeGenerator
from vedo import Plotter, Mesh

def test_vedo_preview():
    """Test vedo 3D preview with different shapes."""
    print("🎯 Testing Vedo 3D Preview")
    print("=" * 30)
    
    # Test shapes
    shapes = [
        ("Rectangular Container", lambda: ShapeGenerator.create_rounded_box(100, 60, 40, 3, 0)),
        ("Rounded Rectangular", lambda: ShapeGenerator.create_rounded_box(100, 60, 40, 3, 5)),
        ("Triangular Container", lambda: ShapeGenerator.create_triangular_container(80, 40, 3, 2)),
        ("Circular Container", lambda: ShapeGenerator.create_circular_container(80, 40, 3, 32)),
    ]
    
    for i, (name, shape_func) in enumerate(shapes, 1):
        print(f"\n{i}. Testing {name}...")
        
        try:
            # Generate mesh
            mesh = shape_func()
            print(f"   ✅ Mesh generated: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
            
            # Convert to vedo mesh
            vedo_mesh = Mesh([mesh.vertices, mesh.faces])
            vedo_mesh.color("lightblue").alpha(0.8).lighting("plastic")
            print(f"   ✅ Vedo mesh created successfully")
            
            # Create plotter and show (non-interactive for testing)
            plotter = Plotter(title=f"Test: {name}", axes=1, bg='white', size=(600, 400))
            
            # Show mesh briefly
            print(f"   ✅ Showing {name} preview...")
            plotter.show(vedo_mesh, f"{name}\n{len(mesh.faces)} faces", interactive=False)
            plotter.close()
            
            print(f"   ✅ {name} preview test completed")
            
        except Exception as e:
            print(f"   ❌ {name} test failed: {e}")
    
    print(f"\n🎉 Vedo preview testing completed!")
    print("💡 The enhanced app now uses vedo for much better 3D visualization!")

if __name__ == "__main__":
    test_vedo_preview()
