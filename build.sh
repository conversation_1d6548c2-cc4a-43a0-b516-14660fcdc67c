#!/bin/bash

# Build script for Bambu Studio Style 3D Container Generator

set -e  # Exit on any error

echo "🎯 Building Bambu Studio Style 3D Container Generator"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check dependencies
print_status "Checking dependencies..."

# Check for CMake
if ! command -v cmake &> /dev/null; then
    print_error "CMake is required but not installed"
    echo "Install with: sudo apt-get install cmake"
    exit 1
fi

# Check for C++ compiler
if ! command -v g++ &> /dev/null; then
    print_error "G++ compiler is required but not installed"
    echo "Install with: sudo apt-get install g++"
    exit 1
fi

# Check for Node.js (for frontend)
if ! command -v node &> /dev/null; then
    print_warning "Node.js not found - frontend development server won't be available"
    echo "Install with: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
else
    print_success "Node.js found: $(node --version)"
fi

# Check for required libraries
print_status "Checking for required libraries..."

# Function to check for library
check_library() {
    local lib_name=$1
    local pkg_name=$2
    
    if pkg-config --exists $lib_name 2>/dev/null; then
        print_success "$lib_name found"
        return 0
    else
        print_warning "$lib_name not found"
        echo "Install with: sudo apt-get install $pkg_name"
        return 1
    fi
}

# Check libraries
missing_libs=0

if ! check_library "glfw3" "libglfw3-dev"; then
    ((missing_libs++))
fi

if ! check_library "glew" "libglew-dev"; then
    ((missing_libs++))
fi

# Check for assimp (might not have pkg-config)
if [ ! -f "/usr/include/assimp/Importer.hpp" ] && [ ! -f "/usr/local/include/assimp/Importer.hpp" ]; then
    print_warning "Assimp not found"
    echo "Install with: sudo apt-get install libassimp-dev"
    ((missing_libs++))
else
    print_success "Assimp found"
fi

if [ $missing_libs -gt 0 ]; then
    print_error "Missing $missing_libs required libraries"
    echo ""
    echo "Install all dependencies with:"
    echo "sudo apt-get update"
    echo "sudo apt-get install cmake g++ libglfw3-dev libglew-dev libassimp-dev"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Build backend
print_status "Building C++ backend..."

cd backend

# Create build directory
if [ ! -d "build" ]; then
    mkdir build
    print_status "Created build directory"
fi

cd build

# Configure with CMake
print_status "Configuring with CMake..."
if cmake .. -DCMAKE_BUILD_TYPE=Release; then
    print_success "CMake configuration successful"
else
    print_error "CMake configuration failed"
    exit 1
fi

# Build
print_status "Compiling C++ backend..."
if make -j$(nproc); then
    print_success "C++ backend compiled successfully"
else
    print_error "C++ backend compilation failed"
    exit 1
fi

cd ../..

# Setup frontend
print_status "Setting up frontend..."

cd frontend

if command -v npm &> /dev/null; then
    print_status "Installing frontend dependencies..."
    if npm install; then
        print_success "Frontend dependencies installed"
    else
        print_warning "Frontend dependency installation failed"
    fi
else
    print_warning "npm not available - skipping frontend setup"
fi

cd ..

# Create run script
print_status "Creating run script..."

cat > run.sh << 'EOF'
#!/bin/bash

# Run script for Bambu Studio Style 3D Container Generator

echo "🚀 Starting Bambu Studio Style 3D Container Generator"
echo "=================================================="

# Start backend
echo "🔧 Starting C++ backend server..."
cd backend/build
./container_generator_server --port 8081 &
BACKEND_PID=$!
cd ../..

# Wait a moment for backend to start
sleep 2

# Start frontend
if command -v npm &> /dev/null; then
    echo "🌐 Starting frontend development server..."
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    echo ""
    echo "✅ Application started successfully!"
    echo "🌐 Frontend: http://localhost:8080"
    echo "🔧 Backend:  http://localhost:8081"
    echo ""
    echo "Press Ctrl+C to stop both servers"
    
    # Wait for interrupt
    trap 'echo ""; echo "🛑 Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT
    wait
else
    echo ""
    echo "✅ Backend started successfully!"
    echo "🔧 Backend: http://localhost:8081"
    echo "🌐 Frontend: Open frontend/index.html in your browser"
    echo ""
    echo "Press Ctrl+C to stop backend server"
    
    # Wait for interrupt
    trap 'echo ""; echo "🛑 Stopping backend..."; kill $BACKEND_PID 2>/dev/null; exit 0' INT
    wait $BACKEND_PID
fi
EOF

chmod +x run.sh

print_success "Run script created"

# Summary
echo ""
echo "🎉 Build completed successfully!"
echo "================================"
echo ""
echo "📁 Project structure:"
echo "   backend/build/container_generator_server  - C++ backend executable"
echo "   frontend/                                 - Web frontend"
echo "   run.sh                                    - Start both servers"
echo ""
echo "🚀 To start the application:"
echo "   ./run.sh"
echo ""
echo "🌐 Access the application at:"
echo "   Frontend: http://localhost:8080"
echo "   Backend:  http://localhost:8081"
echo ""
echo "📋 Features:"
echo "   ✅ Professional Bambu Studio style interface"
echo "   ✅ Three.js 3D visualization"
echo "   ✅ C++ backend with OpenGL rendering"
echo "   ✅ Box, cylinder, and prism generation"
echo "   ✅ Edge manipulation (round/chamfer)"
echo "   ✅ STL export functionality"
echo ""

print_success "Ready to generate 3D containers!"
