<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bambu 3D Suite - Container Generator & Slicer</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        bambu: {
                            orange: '#FF6B35',
                            blue: '#4A90E2',
                            green: '#7ED321',
                            dark: '#1a1a1a',
                            darker: '#0f0f0f',
                            gray: '#2b2b2b',
                            'light-gray': '#3a3a3a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Three.js with proper importmap -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>
    
    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #2b2b2b;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #4A90E2;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #5BA0F2;
        }
        
        /* Glass effect */
        .glass {
            backdrop-filter: blur(10px);
            background: rgba(26, 26, 26, 0.8);
        }
        
        /* Tab animations */
        .tab-content {
            transition: all 0.3s ease-in-out;
        }
        
        /* Progress bar */
        .progress-bar {
            background: linear-gradient(90deg, #FF6B35 0%, #4A90E2 100%);
        }

        /* Movement buttons */
        .movement-btn {
            width: 2.5rem;
            height: 2.5rem;
            background-color: #3a3a3a;
            color: #d1d5db;
            border-radius: 0.5rem;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .movement-btn:hover {
            background-color: #4b5563;
            color: white;
            transform: scale(1.05);
        }

        /* Distance buttons */
        .distance-btn {
            padding: 0.25rem 0.75rem;
            background-color: #3a3a3a;
            color: #d1d5db;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
        }

        .distance-btn:hover {
            background-color: #4b5563;
        }

        .distance-btn.active {
            background-color: #4A90E2;
            color: white;
        }
    </style>
</head>
<body class="bg-bambu-darker text-white font-sans overflow-hidden">
    <!-- Navigation Bar -->
    <nav class="bg-bambu-gray border-b border-bambu-light-gray px-6 py-3">
        <div class="flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center space-x-3">
                <i class="fas fa-cube text-2xl text-bambu-orange"></i>
                <div>
                    <h1 class="text-xl font-bold text-white">Bambu 3D Suite</h1>
                    <p class="text-xs text-gray-400">Professional 3D Design & Printing</p>
                </div>
            </div>
            
            <!-- Tab Navigation -->
            <div class="flex bg-bambu-light-gray rounded-lg p-1">
                <button id="generatorTab" class="px-4 py-2 rounded-md text-sm font-medium transition-all bg-bambu-blue text-white">
                    <i class="fas fa-shapes mr-2"></i>Generator
                </button>
                <button id="slicerTab" class="px-4 py-2 rounded-md text-sm font-medium transition-all text-gray-300 hover:text-white">
                    <i class="fas fa-layer-group mr-2"></i>Slicer
                </button>
                <button id="printerTab" class="px-4 py-2 rounded-md text-sm font-medium transition-all text-gray-300 hover:text-white">
                    <i class="fas fa-print mr-2"></i>Printer
                </button>
            </div>
            
            <!-- Status -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 text-sm">
                    <div id="statusIndicator" class="w-3 h-3 bg-bambu-green rounded-full"></div>
                    <span id="statusText" class="text-gray-300">Ready</span>
                </div>
                <button id="settingsBtn" class="p-2 bg-bambu-light-gray rounded-lg hover:bg-gray-600 transition-all">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>
    </nav>

    <div class="flex h-[calc(100vh-64px)]">
        <!-- Container Generator Tab -->
        <div id="generatorContent" class="flex w-full">
            <!-- Left Sidebar - Generator -->
            <div class="w-80 bg-bambu-gray border-r border-bambu-light-gray flex flex-col custom-scrollbar overflow-y-auto">
                <!-- Shape Configuration -->
                <div class="p-6 space-y-6">
                    <!-- Sides Count -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-shapes mr-2"></i>Shape Sides
                        </label>
                        <div class="relative">
                            <input type="range" id="sidesSlider" min="1" max="12" value="4" 
                                   class="w-full h-2 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-400 mt-1">
                                <span>1 (Cylinder)</span>
                                <span>4 (Box)</span>
                                <span>12 (Dodecagon)</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-300">Current Shape:</span>
                            <span id="shapeType" class="text-bambu-orange font-semibold">Box</span>
                        </div>
                    </div>
                    
                    <!-- Dynamic Configuration Panel -->
                    <div id="configPanel" class="space-y-4">
                        <!-- Will be populated dynamically -->
                    </div>
                    
                    <!-- Render Options -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-eye mr-2"></i>Render Mode
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="solidMode" class="px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600">
                                <i class="fas fa-cube mr-2"></i>Solid
                            </button>
                            <button id="wireframeMode" class="px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                                <i class="fas fa-project-diagram mr-2"></i>Wireframe
                            </button>
                        </div>
                    </div>
                    
                    <!-- Export Section -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-download mr-2"></i>Export
                        </label>
                        <button id="exportSTL" class="w-full px-4 py-3 bg-gradient-to-r from-bambu-orange to-bambu-blue text-white rounded-lg font-semibold transition-all hover:shadow-lg hover:scale-105">
                            <i class="fas fa-file-export mr-2"></i>Export STL
                        </button>
                        <button id="sendToSlicer" class="w-full px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-arrow-right mr-2"></i>Send to Slicer
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Main Viewport - Generator -->
            <div class="flex-1 relative bg-bambu-darker">
                <canvas id="canvas3d" class="w-full h-full"></canvas>
                
                <!-- Viewport Controls -->
                <div class="absolute top-4 right-4 flex flex-col space-y-2">
                    <button id="resetView" class="p-2 glass rounded-lg hover:bg-gray-600 transition-all">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button id="fitView" class="p-2 glass rounded-lg hover:bg-gray-600 transition-all">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button id="gridToggle" class="p-2 glass rounded-lg hover:bg-gray-600 transition-all">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
                
                <!-- Info Panel -->
                <div class="absolute bottom-4 left-4 glass rounded-lg p-3">
                    <div class="text-sm text-gray-300">
                        <div>Shape: <span id="currentShapeInfo" class="text-bambu-orange">Box</span></div>
                        <div>Triangles: <span id="triangleCount" class="text-white">0</span></div>
                        <div>Volume: <span id="volumeInfo" class="text-white">0 cm³</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slicer Tab -->
        <div id="slicerContent" class="flex w-full hidden">
            <!-- Left Sidebar - Slicer -->
            <div class="w-80 bg-bambu-gray border-r border-bambu-light-gray flex flex-col custom-scrollbar overflow-y-auto">
                <div class="p-6 space-y-6">
                    <!-- Object Manager -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-cubes mr-2"></i>Object Manager
                        </label>
                        <div class="space-y-2">
                            <button id="loadSTL" class="w-full px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                                <i class="fas fa-folder-open mr-2"></i>Load STL/OBJ
                            </button>
                            <button id="clearAll" class="w-full px-4 py-2 bg-red-500 text-white rounded-lg transition-all hover:bg-red-600">
                                <i class="fas fa-trash mr-2"></i>Clear All Objects
                            </button>
                        </div>

                        <!-- Object List -->
                        <div id="objectList" class="space-y-2 max-h-32 overflow-y-auto custom-scrollbar">
                            <div class="text-xs text-gray-400 text-center py-2">No objects loaded</div>
                        </div>
                    </div>
                    
                    <!-- Print Settings -->
                    <div class="space-y-4">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-cogs mr-2"></i>Print Settings
                        </label>
                        
                        <!-- Layer Height -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Layer Height (mm)</label>
                            <select id="layerHeight" class="w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white">
                                <option value="0.1">0.1mm (Ultra Fine)</option>
                                <option value="0.15">0.15mm (Fine)</option>
                                <option value="0.2" selected>0.2mm (Standard)</option>
                                <option value="0.25">0.25mm (Fast)</option>
                                <option value="0.3">0.3mm (Draft)</option>
                            </select>
                        </div>
                        
                        <!-- Infill -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Infill Density (%)</label>
                            <input type="range" id="infillDensity" min="0" max="100" value="20" 
                                   class="w-full h-2 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-400">
                                <span>0%</span>
                                <span id="infillValue">20%</span>
                                <span>100%</span>
                            </div>
                        </div>
                        
                        <!-- Support Structures -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Support Structures</label>
                            <div class="grid grid-cols-2 gap-2">
                                <button id="supportNone" class="px-3 py-2 bg-bambu-blue text-white rounded-lg text-xs">
                                    None
                                </button>
                                <button id="supportAuto" class="px-3 py-2 bg-bambu-light-gray text-gray-300 rounded-lg text-xs">
                                    Auto
                                </button>
                            </div>
                        </div>
                        
                        <!-- Print Speed -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Print Speed (mm/s)</label>
                            <input type="number" id="printSpeed" value="50" min="10" max="200" 
                                   class="w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white">
                        </div>
                    </div>
                    
                    <!-- Material Settings -->
                    <div class="space-y-4">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-thermometer-half mr-2"></i>Material
                        </label>
                        
                        <!-- Material Type -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Filament Type</label>
                            <select id="materialType" class="w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white">
                                <option value="pla">PLA (1.24 g/cm³)</option>
                                <option value="abs">ABS (1.04 g/cm³)</option>
                                <option value="petg">PETG (1.27 g/cm³)</option>
                                <option value="tpu">TPU (1.20 g/cm³)</option>
                            </select>
                        </div>
                        
                        <!-- Nozzle Temperature -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Nozzle Temperature (°C)</label>
                            <input type="number" id="nozzleTemp" value="210" min="180" max="300" 
                                   class="w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white">
                        </div>
                        
                        <!-- Bed Temperature -->
                        <div class="space-y-2">
                            <label class="block text-xs text-gray-300">Bed Temperature (°C)</label>
                            <input type="number" id="bedTemp" value="60" min="0" max="120" 
                                   class="w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white">
                        </div>
                    </div>
                    
                    <!-- Slice Button -->
                    <div class="space-y-3">
                        <button id="sliceModel" class="w-full px-4 py-3 bg-gradient-to-r from-bambu-orange to-bambu-blue text-white rounded-lg font-semibold transition-all hover:shadow-lg hover:scale-105">
                            <i class="fas fa-layer-group mr-2"></i>Slice Model
                        </button>
                        
                        <!-- Progress Bar -->
                        <div id="sliceProgress" class="hidden">
                            <div class="w-full bg-bambu-light-gray rounded-full h-2">
                                <div id="progressBar" class="progress-bar h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-gray-400 mt-1 text-center">
                                <span id="progressText">Preparing...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Viewport - Slicer -->
            <div class="flex-1 relative bg-bambu-darker" style="background-color: #0f0f0f; height: calc(100vh - 64px);">
                <canvas id="slicerCanvas" class="w-full h-full" style="width: 100%; height: 100%; display: block;"></canvas>
                
                <!-- Layer Controls -->
                <div class="absolute top-4 left-4 glass rounded-lg p-3 space-y-2">
                    <div class="text-sm font-semibold text-white">Layer Preview</div>
                    <div class="flex items-center space-x-2">
                        <button id="layerPrev" class="p-1 bg-bambu-light-gray rounded hover:bg-gray-600">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </button>
                        <span id="currentLayer" class="text-xs text-gray-300 min-w-[60px] text-center">0 / 0</span>
                        <button id="layerNext" class="p-1 bg-bambu-light-gray rounded hover:bg-gray-600">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </button>
                    </div>
                    <input type="range" id="layerSlider" min="0" max="0" value="0"
                           class="w-full h-1 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer">
                    <div class="text-xs text-gray-400">
                        Height: <span id="layerHeight">0.00mm</span>
                    </div>
                </div>
                
                <!-- Print Estimation -->
                <div class="absolute bottom-4 left-4 glass rounded-lg p-4 space-y-2">
                    <div class="text-sm font-semibold text-bambu-orange">Print Estimation</div>
                    <div class="grid grid-cols-2 gap-4 text-xs">
                        <div>
                            <div class="text-gray-400">Print Time:</div>
                            <div id="printTime" class="text-white font-semibold">--:--</div>
                        </div>
                        <div>
                            <div class="text-gray-400">Material:</div>
                            <div id="materialUsage" class="text-white font-semibold">-- g</div>
                        </div>
                        <div>
                            <div class="text-gray-400">Layers:</div>
                            <div id="totalLayers" class="text-white font-semibold">--</div>
                        </div>
                        <div>
                            <div class="text-gray-400">Cost:</div>
                            <div id="printCost" class="text-white font-semibold">$--</div>
                        </div>
                    </div>
                </div>
                
                <!-- Export G-code -->
                <div class="absolute bottom-4 right-4">
                    <button id="exportGcode" class="px-6 py-3 bg-gradient-to-r from-bambu-green to-bambu-blue text-white rounded-lg font-semibold transition-all hover:shadow-lg hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-download mr-2"></i>Export G-code
                    </button>
                </div>
            </div>
        </div>
    </div>

        <!-- Printer Monitor Tab -->
        <div id="printerContent" class="flex w-full hidden" style="height: calc(100vh - 64px);">
            <!-- Left Sidebar - Printer Controls -->
            <div class="w-80 bg-bambu-gray border-r border-bambu-light-gray flex flex-col custom-scrollbar overflow-y-auto" style="background-color: #2b2b2b; border-right: 1px solid #3a3a3a; height: 100%;">
                <div class="p-6 space-y-6" style="color: white;">
                    <!-- Printer Status -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue" style="color: #4A90E2;">
                            <i class="fas fa-print mr-2"></i>Printer Status
                        </label>
                        <div class="bg-bambu-light-gray rounded-lg p-4 space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">Connection:</span>
                                <div class="flex items-center space-x-2">
                                    <div id="connectionStatus" class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span id="connectionText" class="text-sm text-gray-300">Disconnected</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">State:</span>
                                <span id="printerState" class="text-sm text-white">Idle</span>
                            </div>
                            <button id="connectPrinter" class="w-full px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600">
                                <i class="fas fa-plug mr-2"></i>Connect Printer
                            </button>
                        </div>
                    </div>

                    <!-- Movement Controls -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-arrows-alt mr-2"></i>Movement Controls
                        </label>
                        <div class="bg-bambu-light-gray rounded-lg p-4 space-y-4">
                            <!-- XY Movement -->
                            <div class="text-center">
                                <div class="grid grid-cols-3 gap-2 mb-3">
                                    <div></div>
                                    <button class="movement-btn" data-axis="y" data-direction="1">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <div></div>
                                    <button class="movement-btn" data-axis="x" data-direction="-1">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button id="homeXY" class="movement-btn bg-bambu-orange">
                                        <i class="fas fa-home"></i>
                                    </button>
                                    <button class="movement-btn" data-axis="x" data-direction="1">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                    <div></div>
                                    <button class="movement-btn" data-axis="y" data-direction="-1">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                    <div></div>
                                </div>
                                <div class="text-xs text-gray-400">XY Movement</div>
                            </div>

                            <!-- Z Movement -->
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">Z Axis:</span>
                                <div class="flex items-center space-x-2">
                                    <button class="movement-btn" data-axis="z" data-direction="1">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <button id="homeZ" class="movement-btn bg-bambu-orange">
                                        <i class="fas fa-home"></i>
                                    </button>
                                    <button class="movement-btn" data-axis="z" data-direction="-1">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Movement Distance -->
                            <div class="space-y-2">
                                <label class="block text-xs text-gray-300">Movement Distance (mm)</label>
                                <div class="grid grid-cols-4 gap-1">
                                    <button class="distance-btn active" data-distance="0.1">0.1</button>
                                    <button class="distance-btn" data-distance="1">1</button>
                                    <button class="distance-btn" data-distance="10">10</button>
                                    <button class="distance-btn" data-distance="100">100</button>
                                </div>
                            </div>

                            <!-- Home All -->
                            <button id="homeAll" class="w-full px-4 py-2 bg-gradient-to-r from-bambu-orange to-bambu-blue text-white rounded-lg font-semibold">
                                <i class="fas fa-home mr-2"></i>Home All Axes
                            </button>
                        </div>
                    </div>

                    <!-- Filament Controls -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-tape mr-2"></i>Filament Controls
                        </label>
                        <div class="bg-bambu-light-gray rounded-lg p-4 space-y-3">
                            <div class="grid grid-cols-2 gap-2">
                                <button id="loadFilament" class="px-4 py-2 bg-bambu-green text-white rounded-lg transition-all hover:bg-green-600">
                                    <i class="fas fa-arrow-down mr-2"></i>Load
                                </button>
                                <button id="unloadFilament" class="px-4 py-2 bg-red-500 text-white rounded-lg transition-all hover:bg-red-600">
                                    <i class="fas fa-arrow-up mr-2"></i>Unload
                                </button>
                            </div>
                            <div class="space-y-2">
                                <label class="block text-xs text-gray-300">Extrude Length (mm)</label>
                                <input type="number" id="extrudeLength" value="10" min="1" max="100"
                                       class="w-full px-3 py-2 bg-bambu-darker border border-gray-600 rounded-lg text-white">
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                                <button id="extrudeFilament" class="px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600">
                                    <i class="fas fa-plus mr-2"></i>Extrude
                                </button>
                                <button id="retractFilament" class="px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600">
                                    <i class="fas fa-minus mr-2"></i>Retract
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Temperature Controls -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-bambu-blue">
                            <i class="fas fa-thermometer-half mr-2"></i>Temperature
                        </label>
                        <div class="bg-bambu-light-gray rounded-lg p-4 space-y-4">
                            <!-- Nozzle Temperature -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-300">Nozzle:</span>
                                    <span id="nozzleTempCurrent" class="text-white font-semibold">25°C</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="nozzleTempTarget" value="210" min="0" max="300"
                                           class="flex-1 px-3 py-1 bg-bambu-darker border border-gray-600 rounded text-white text-sm">
                                    <button id="setNozzleTemp" class="px-3 py-1 bg-bambu-orange text-white rounded text-sm">
                                        Set
                                    </button>
                                </div>
                            </div>

                            <!-- Bed Temperature -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-300">Bed:</span>
                                    <span id="bedTempCurrent" class="text-white font-semibold">25°C</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="bedTempTarget" value="60" min="0" max="120"
                                           class="flex-1 px-3 py-1 bg-bambu-darker border border-gray-600 rounded text-white text-sm">
                                    <button id="setBedTemp" class="px-3 py-1 bg-bambu-orange text-white rounded text-sm">
                                        Set
                                    </button>
                                </div>
                            </div>

                            <!-- Emergency Stop -->
                            <button id="emergencyStop" class="w-full px-4 py-2 bg-red-600 text-white rounded-lg font-semibold transition-all hover:bg-red-700">
                                <i class="fas fa-stop mr-2"></i>EMERGENCY STOP
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Area - Camera and Print Progress -->
            <div class="flex-1 flex flex-col bg-bambu-darker">
                <!-- Camera Feed -->
                <div class="flex-1 relative">
                    <div id="cameraFeed" class="w-full h-full bg-bambu-darker flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-video text-6xl text-gray-600 mb-4"></i>
                            <div class="text-gray-400">Camera Feed</div>
                            <div class="text-sm text-gray-500">Connect printer to view camera</div>
                        </div>
                    </div>

                    <!-- Camera Controls -->
                    <div class="absolute top-4 right-4 flex space-x-2">
                        <button id="cameraSnapshot" class="p-2 glass rounded-lg hover:bg-gray-600 transition-all">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button id="cameraFullscreen" class="p-2 glass rounded-lg hover:bg-gray-600 transition-all">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Print Progress Panel -->
                <div class="h-48 bg-bambu-gray border-t border-bambu-light-gray p-6">
                    <div class="grid grid-cols-3 gap-6 h-full">
                        <!-- Current Print Info -->
                        <div class="space-y-3">
                            <h3 class="text-sm font-semibold text-bambu-orange">Current Print</h3>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">File:</span>
                                    <span id="currentFile" class="text-white">No file loaded</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Layer:</span>
                                    <span id="currentPrintLayer" class="text-white">0 / 0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Progress:</span>
                                    <span id="printProgress" class="text-white">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Time Information -->
                        <div class="space-y-3">
                            <h3 class="text-sm font-semibold text-bambu-orange">Time</h3>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Elapsed:</span>
                                    <span id="timeElapsed" class="text-white">00:00:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Remaining:</span>
                                    <span id="timeRemaining" class="text-white">--:--:--</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Total:</span>
                                    <span id="timeTotal" class="text-white">--:--:--</span>
                                </div>
                            </div>
                        </div>

                        <!-- Print Controls -->
                        <div class="space-y-3">
                            <h3 class="text-sm font-semibold text-bambu-orange">Controls</h3>
                            <div class="grid grid-cols-2 gap-2">
                                <button id="pausePrint" class="px-4 py-2 bg-bambu-orange text-white rounded-lg transition-all hover:bg-orange-600 disabled:opacity-50" disabled>
                                    <i class="fas fa-pause mr-2"></i>Pause
                                </button>
                                <button id="resumePrint" class="px-4 py-2 bg-bambu-green text-white rounded-lg transition-all hover:bg-green-600 disabled:opacity-50" disabled>
                                    <i class="fas fa-play mr-2"></i>Resume
                                </button>
                                <button id="stopPrint" class="px-4 py-2 bg-red-500 text-white rounded-lg transition-all hover:bg-red-600 disabled:opacity-50" disabled>
                                    <i class="fas fa-stop mr-2"></i>Stop
                                </button>
                                <button id="startPrint" class="px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600">
                                    <i class="fas fa-play mr-2"></i>Start
                                </button>
                            </div>

                            <!-- Progress Bar -->
                            <div class="space-y-1">
                                <div class="w-full bg-bambu-light-gray rounded-full h-2">
                                    <div id="printProgressBar" class="progress-bar h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div class="text-xs text-gray-400 text-center">
                                    <span id="progressPercentage">0%</span> Complete
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <script type="module">
        // Import Three.js
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { STLLoader } from 'three/addons/loaders/STLLoader.js';
        
        // Global application state
        const app = {
            currentTab: 'generator',
            generator: {
                scene: null,
                camera: null,
                renderer: null,
                controls: null,
                currentMesh: null,
                currentSides: 4
            },
            slicer: {
                scene: null,
                camera: null,
                renderer: null,
                controls: null,
                loadedModel: null,
                layers: [],
                currentLayer: 0,
                sliceSettings: {
                    layerHeight: 0.2,
                    infillDensity: 20,
                    supportStructures: false,
                    printSpeed: 50,
                    materialType: 'pla',
                    nozzleTemp: 210,
                    bedTemp: 60
                }
            },
            printer: {
                connected: false,
                state: 'idle',
                position: { x: 0, y: 0, z: 0 },
                temperatures: { nozzle: 25, bed: 25, targetNozzle: 210, targetBed: 60 },
                currentPrint: {
                    file: null,
                    progress: 0,
                    layer: 0,
                    totalLayers: 0,
                    timeElapsed: 0,
                    timeRemaining: 0,
                    timeTotal: 0
                },
                movementDistance: 0.1
            }
        };
        
        // Material densities (g/cm³)
        const materialDensities = {
            pla: 1.24,
            abs: 1.04,
            petg: 1.27,
            tpu: 1.20
        };
        
        // Initialize application
        async function init() {
            try {
                console.log('🎯 Initializing Bambu 3D Suite');
                updateStatus('Initializing...', 'loading');

                // Remove any existing loading indicators
                const existingLoading = document.querySelector('[style*="BAMBU 3D SUITE LOADING"]');
                if (existingLoading) existingLoading.remove();

                console.log('🔧 Initializing generator...');
                await initGenerator();

                console.log('🍰 Initializing slicer...');
                await initSlicer();

                console.log('🎮 Setting up event listeners...');
                setupEventListeners();

                // Remove loading indicator
                setTimeout(() => {
                    const loadingDiv = document.querySelector('[style*="BAMBU 3D SUITE LOADING"]');
                    if (loadingDiv) loadingDiv.remove();
                }, 2000);

                updateStatus('Ready - Design and slice your models', 'success');
                console.log('✅ Bambu 3D Suite initialized successfully');

            } catch (error) {
                console.error('❌ Initialization failed:', error);
                updateStatus('Initialization failed', 'error');

                // Show error on page
                document.body.insertAdjacentHTML('afterbegin',
                    `<div style="position: fixed; top: 50px; right: 0; background: red; color: white; padding: 10px; z-index: 9999;">❌ ERROR: ${error.message}</div>`
                );
            }
        }
        
        async function initGenerator() {
            const canvas = document.getElementById('canvas3d');

            // Create scene
            app.generator.scene = new THREE.Scene();
            app.generator.scene.background = new THREE.Color(0x0f0f0f);

            // Create camera
            const aspect = canvas.clientWidth / canvas.clientHeight;
            app.generator.camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
            app.generator.camera.position.set(150, 100, 150);

            // Create renderer
            app.generator.renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
            app.generator.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            app.generator.renderer.setPixelRatio(window.devicePixelRatio);
            app.generator.renderer.shadowMap.enabled = true;

            // Create controls
            app.generator.controls = new OrbitControls(app.generator.camera, canvas);
            app.generator.controls.enableDamping = true;
            app.generator.controls.dampingFactor = 0.05;

            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            app.generator.scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            app.generator.scene.add(directionalLight);

            // Add helpers
            const gridHelper = new THREE.GridHelper(200, 20, 0x333333, 0x333333);
            app.generator.scene.add(gridHelper);

            const axesHelper = new THREE.AxesHelper(50);
            app.generator.scene.add(axesHelper);

            // Start animation loop
            animateGenerator();

            // Generate initial geometry
            updateShapeConfig();
            updateGeometry();
        }

        async function initSlicer() {
            console.log('🔧 Initializing slicer...');

            // Wait for DOM to be ready
            await new Promise(resolve => setTimeout(resolve, 100));

            const canvas = document.getElementById('slicerCanvas');
            if (!canvas) {
                console.error('❌ Slicer canvas not found');
                return;
            }

            console.log('📐 Canvas found, dimensions:', canvas.clientWidth, 'x', canvas.clientHeight);

            // Force canvas to have minimum size
            if (canvas.clientWidth === 0 || canvas.clientHeight === 0) {
                canvas.style.width = '800px';
                canvas.style.height = '600px';
                console.log('🔧 Fixed canvas dimensions to 800x600');
            }

            // Create scene
            app.slicer.scene = new THREE.Scene();
            app.slicer.scene.background = new THREE.Color(0x1a1a1a);

            // Create camera
            const aspect = canvas.clientWidth / canvas.clientHeight || (800/600);
            app.slicer.camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
            app.slicer.camera.position.set(150, 100, 150);
            app.slicer.camera.lookAt(0, 0, 0);

            // Create renderer
            app.slicer.renderer = new THREE.WebGLRenderer({
                canvas,
                antialias: true,
                alpha: false
            });
            app.slicer.renderer.setSize(canvas.clientWidth || 800, canvas.clientHeight || 600);
            app.slicer.renderer.setPixelRatio(window.devicePixelRatio);
            app.slicer.renderer.shadowMap.enabled = true;
            app.slicer.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create controls
            app.slicer.controls = new OrbitControls(app.slicer.camera, canvas);
            app.slicer.controls.enableDamping = true;
            app.slicer.controls.dampingFactor = 0.05;
            app.slicer.controls.minDistance = 10;
            app.slicer.controls.maxDistance = 500;

            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            app.slicer.scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            app.slicer.scene.add(directionalLight);

            // Add build plate
            const plateGeometry = new THREE.PlaneGeometry(220, 220);
            const plateMaterial = new THREE.MeshLambertMaterial({
                color: 0x444444,
                transparent: false
            });
            const buildPlate = new THREE.Mesh(plateGeometry, plateMaterial);
            buildPlate.rotation.x = -Math.PI / 2;
            buildPlate.receiveShadow = true;
            app.slicer.scene.add(buildPlate);

            // Add grid
            const gridHelper = new THREE.GridHelper(220, 22, 0x666666, 0x333333);
            app.slicer.scene.add(gridHelper);

            // Initialize empty object manager
            app.slicer.loadedModels = []; // Array to manage multiple objects

            // Start animation loop
            animateSlicer();

            // Force initial render
            app.slicer.renderer.render(app.slicer.scene, app.slicer.camera);

            console.log('✅ Slicer initialized successfully with test cube');
            console.log('📊 Scene objects:', app.slicer.scene.children.length);
        }

        function animateGenerator() {
            requestAnimationFrame(animateGenerator);
            if (app.generator.controls) app.generator.controls.update();
            if (app.generator.renderer && app.generator.scene && app.generator.camera) {
                app.generator.renderer.render(app.generator.scene, app.generator.camera);
            }
        }

        let slicerAnimationId = null;
        function animateSlicer() {
            if (slicerAnimationId) {
                cancelAnimationFrame(slicerAnimationId);
            }

            function animate() {
                slicerAnimationId = requestAnimationFrame(animate);

                try {
                    if (app.slicer.controls) {
                        app.slicer.controls.update();
                    }

                    if (app.slicer.renderer && app.slicer.scene && app.slicer.camera) {
                        app.slicer.renderer.render(app.slicer.scene, app.slicer.camera);
                    }
                } catch (error) {
                    console.error('Slicer animation error:', error);
                }
            }

            console.log('🎬 Starting slicer animation loop');
            animate();
        }

        function setupEventListeners() {
            // Tab switching
            document.getElementById('generatorTab').addEventListener('click', () => switchTab('generator'));
            document.getElementById('slicerTab').addEventListener('click', () => switchTab('slicer'));
            document.getElementById('printerTab').addEventListener('click', () => switchTab('printer'));

            // Generator controls
            document.getElementById('sidesSlider').addEventListener('input', (e) => {
                app.generator.currentSides = parseInt(e.target.value);
                updateShapeConfig();
                updateGeometry();
            });

            document.getElementById('solidMode').addEventListener('click', () => setRenderMode('solid'));
            document.getElementById('wireframeMode').addEventListener('click', () => setRenderMode('wireframe'));
            document.getElementById('resetView').addEventListener('click', resetGeneratorView);
            document.getElementById('fitView').addEventListener('click', fitGeneratorView);
            document.getElementById('exportSTL').addEventListener('click', exportSTL);
            document.getElementById('sendToSlicer').addEventListener('click', sendToSlicer);

            // Slicer controls
            document.getElementById('loadSTL').addEventListener('click', loadSTLFile);
            document.getElementById('clearAll').addEventListener('click', clearAllObjects);
            document.getElementById('sliceModel').addEventListener('click', sliceModel);
            document.getElementById('exportGcode').addEventListener('click', exportGcode);

            // Slicer settings
            document.getElementById('layerHeight').addEventListener('change', updateSliceSettings);
            document.getElementById('infillDensity').addEventListener('input', updateInfillDisplay);
            document.getElementById('materialType').addEventListener('change', updateMaterialSettings);
            document.getElementById('printSpeed').addEventListener('input', updateSliceSettings);
            document.getElementById('nozzleTemp').addEventListener('input', updateSliceSettings);
            document.getElementById('bedTemp').addEventListener('input', updateSliceSettings);

            // Support buttons
            document.getElementById('supportNone').addEventListener('click', () => setSupportMode(false));
            document.getElementById('supportAuto').addEventListener('click', () => setSupportMode(true));

            // Layer navigation
            document.getElementById('layerPrev').addEventListener('click', () => changeLayer(-1));
            document.getElementById('layerNext').addEventListener('click', () => changeLayer(1));
            document.getElementById('layerSlider').addEventListener('input', (e) => {
                app.slicer.currentLayer = parseInt(e.target.value);
                updateLayerDisplay();
            });

            // Printer controls
            document.getElementById('connectPrinter').addEventListener('click', togglePrinterConnection);
            document.getElementById('homeAll').addEventListener('click', () => homePrinter('all'));
            document.getElementById('homeXY').addEventListener('click', () => homePrinter('xy'));
            document.getElementById('homeZ').addEventListener('click', () => homePrinter('z'));

            // Movement controls
            document.querySelectorAll('.movement-btn[data-axis]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const axis = e.target.closest('.movement-btn').dataset.axis;
                    const direction = parseInt(e.target.closest('.movement-btn').dataset.direction);
                    movePrinter(axis, direction);
                });
            });

            // Distance buttons
            document.querySelectorAll('.distance-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.distance-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    app.printer.movementDistance = parseFloat(e.target.dataset.distance);
                });
            });

            // Filament controls
            document.getElementById('loadFilament').addEventListener('click', () => controlFilament('load'));
            document.getElementById('unloadFilament').addEventListener('click', () => controlFilament('unload'));
            document.getElementById('extrudeFilament').addEventListener('click', () => controlFilament('extrude'));
            document.getElementById('retractFilament').addEventListener('click', () => controlFilament('retract'));

            // Temperature controls
            document.getElementById('setNozzleTemp').addEventListener('click', setNozzleTemperature);
            document.getElementById('setBedTemp').addEventListener('click', setBedTemperature);
            document.getElementById('emergencyStop').addEventListener('click', emergencyStop);

            // Print controls
            document.getElementById('startPrint').addEventListener('click', startPrint);
            document.getElementById('pausePrint').addEventListener('click', pausePrint);
            document.getElementById('resumePrint').addEventListener('click', resumePrint);
            document.getElementById('stopPrint').addEventListener('click', stopPrint);

            // Camera controls
            document.getElementById('cameraSnapshot').addEventListener('click', takeSnapshot);
            document.getElementById('cameraFullscreen').addEventListener('click', toggleCameraFullscreen);

            // Window resize
            window.addEventListener('resize', onWindowResize);
        }

        function switchTab(tab) {
            app.currentTab = tab;

            // Update tab buttons
            const generatorTab = document.getElementById('generatorTab');
            const slicerTab = document.getElementById('slicerTab');
            const printerTab = document.getElementById('printerTab');

            // Reset all tabs
            generatorTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all text-gray-300 hover:text-white';
            slicerTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all text-gray-300 hover:text-white';
            printerTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all text-gray-300 hover:text-white';

            // Hide all content
            document.getElementById('generatorContent').classList.add('hidden');
            document.getElementById('slicerContent').classList.add('hidden');
            document.getElementById('printerContent').classList.add('hidden');

            // Show selected tab
            if (tab === 'generator') {
                generatorTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all bg-bambu-blue text-white';
                document.getElementById('generatorContent').classList.remove('hidden');
            } else if (tab === 'slicer') {
                slicerTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all bg-bambu-blue text-white';
                document.getElementById('slicerContent').classList.remove('hidden');
                console.log('🔄 Switched to slicer tab');
            } else if (tab === 'printer') {
                printerTab.className = 'px-4 py-2 rounded-md text-sm font-medium transition-all bg-bambu-blue text-white';
                document.getElementById('printerContent').classList.remove('hidden');
                console.log('🖨️ Switched to printer tab');
            }

            // Trigger resize to update canvases
            setTimeout(() => {
                onWindowResize();

                // Force slicer canvas update if switching to slicer
                if (tab === 'slicer' && app.slicer.renderer) {
                    const canvas = document.getElementById('slicerCanvas');
                    if (canvas) {
                        console.log('🔧 Updating slicer canvas size:', canvas.clientWidth, 'x', canvas.clientHeight);

                        // Ensure canvas has proper dimensions
                        if (canvas.clientWidth === 0 || canvas.clientHeight === 0) {
                            canvas.style.width = '100%';
                            canvas.style.height = '100%';
                            // Wait for style to apply
                            setTimeout(() => {
                                app.slicer.renderer.setSize(canvas.clientWidth || 800, canvas.clientHeight || 600);
                                app.slicer.camera.aspect = (canvas.clientWidth || 800) / (canvas.clientHeight || 600);
                                app.slicer.camera.updateProjectionMatrix();
                                app.slicer.renderer.render(app.slicer.scene, app.slicer.camera);
                            }, 50);
                        } else {
                            app.slicer.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                            app.slicer.camera.aspect = canvas.clientWidth / canvas.clientHeight;
                            app.slicer.camera.updateProjectionMatrix();
                            app.slicer.renderer.render(app.slicer.scene, app.slicer.camera);
                        }
                    }
                }

                // Debug printer tab visibility
                if (tab === 'printer') {
                    const printerContent = document.getElementById('printerContent');
                    console.log('🖨️ Printer content visibility:', printerContent ? 'found' : 'not found');
                    if (printerContent) {
                        console.log('🖨️ Printer content classes:', printerContent.className);
                        console.log('🖨️ Printer content children:', printerContent.children.length);
                    }
                }
            }, 100);
        }

        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('statusText');
            const statusIndicator = document.getElementById('statusIndicator');

            statusText.textContent = message;

            statusIndicator.className = 'w-3 h-3 rounded-full ';
            switch (type) {
                case 'success':
                    statusIndicator.className += 'bg-bambu-green';
                    break;
                case 'error':
                    statusIndicator.className += 'bg-red-500 animate-pulse';
                    break;
                case 'loading':
                    statusIndicator.className += 'bg-bambu-orange animate-pulse';
                    break;
                default:
                    statusIndicator.className += 'bg-bambu-blue';
            }
        }

        function onWindowResize() {
            if (app.currentTab === 'generator') {
                const canvas = document.getElementById('canvas3d');
                if (canvas && app.generator.camera && app.generator.renderer) {
                    app.generator.camera.aspect = canvas.clientWidth / canvas.clientHeight;
                    app.generator.camera.updateProjectionMatrix();
                    app.generator.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                }
            } else if (app.currentTab === 'slicer') {
                const canvas = document.getElementById('slicerCanvas');
                if (canvas && app.slicer.camera && app.slicer.renderer) {
                    app.slicer.camera.aspect = canvas.clientWidth / canvas.clientHeight;
                    app.slicer.camera.updateProjectionMatrix();
                    app.slicer.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                }
            }
        }

        // Printer Control Functions
        function togglePrinterConnection() {
            app.printer.connected = !app.printer.connected;

            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            const connectBtn = document.getElementById('connectPrinter');

            if (app.printer.connected) {
                statusIndicator.className = 'w-3 h-3 bg-bambu-green rounded-full';
                statusText.textContent = 'Connected';
                connectBtn.innerHTML = '<i class="fas fa-unlink mr-2"></i>Disconnect';
                connectBtn.className = 'w-full px-4 py-2 bg-red-500 text-white rounded-lg transition-all hover:bg-red-600';

                // Start temperature simulation
                startTemperatureSimulation();
                updateStatus('Printer connected', 'success');
            } else {
                statusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full';
                statusText.textContent = 'Disconnected';
                connectBtn.innerHTML = '<i class="fas fa-plug mr-2"></i>Connect Printer';
                connectBtn.className = 'w-full px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600';

                updateStatus('Printer disconnected', 'info');
            }
        }

        function startTemperatureSimulation() {
            // Simulate temperature readings
            setInterval(() => {
                if (app.printer.connected) {
                    // Simulate temperature fluctuations
                    const nozzleTemp = app.printer.temperatures.targetNozzle + (Math.random() - 0.5) * 5;
                    const bedTemp = app.printer.temperatures.targetBed + (Math.random() - 0.5) * 3;

                    app.printer.temperatures.nozzle = Math.max(20, nozzleTemp);
                    app.printer.temperatures.bed = Math.max(20, bedTemp);

                    document.getElementById('nozzleTempCurrent').textContent = `${Math.round(app.printer.temperatures.nozzle)}°C`;
                    document.getElementById('bedTempCurrent').textContent = `${Math.round(app.printer.temperatures.bed)}°C`;
                }
            }, 2000);
        }

        function homePrinter(axis) {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            updateStatus(`Homing ${axis.toUpperCase()}...`, 'loading');

            // Simulate homing
            setTimeout(() => {
                if (axis === 'all' || axis === 'xy') {
                    app.printer.position.x = 0;
                    app.printer.position.y = 0;
                }
                if (axis === 'all' || axis === 'z') {
                    app.printer.position.z = 0;
                }

                updateStatus(`${axis.toUpperCase()} homed successfully`, 'success');
            }, 2000);
        }

        function movePrinter(axis, direction) {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            const distance = app.printer.movementDistance * direction;
            app.printer.position[axis] += distance;

            updateStatus(`Moved ${axis.toUpperCase()} ${distance > 0 ? '+' : ''}${distance}mm`, 'success');
        }

        function controlFilament(action) {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            const length = parseFloat(document.getElementById('extrudeLength').value) || 10;

            updateStatus(`${action.charAt(0).toUpperCase() + action.slice(1)}ing filament...`, 'loading');

            setTimeout(() => {
                updateStatus(`Filament ${action} completed`, 'success');
            }, 3000);
        }

        function setNozzleTemperature() {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            const temp = parseInt(document.getElementById('nozzleTempTarget').value);
            app.printer.temperatures.targetNozzle = temp;
            updateStatus(`Nozzle temperature set to ${temp}°C`, 'success');
        }

        function setBedTemperature() {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            const temp = parseInt(document.getElementById('bedTempTarget').value);
            app.printer.temperatures.targetBed = temp;
            updateStatus(`Bed temperature set to ${temp}°C`, 'success');
        }

        function emergencyStop() {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            app.printer.state = 'stopped';
            document.getElementById('printerState').textContent = 'Emergency Stop';
            updateStatus('EMERGENCY STOP ACTIVATED', 'error');
        }

        function startPrint() {
            if (!app.printer.connected) {
                updateStatus('Printer not connected', 'error');
                return;
            }

            app.printer.state = 'printing';
            document.getElementById('printerState').textContent = 'Printing';
            document.getElementById('pausePrint').disabled = false;
            document.getElementById('stopPrint').disabled = false;
            document.getElementById('startPrint').disabled = true;

            // Simulate print progress
            simulatePrintProgress();
            updateStatus('Print started', 'success');
        }

        function pausePrint() {
            app.printer.state = 'paused';
            document.getElementById('printerState').textContent = 'Paused';
            document.getElementById('pausePrint').disabled = true;
            document.getElementById('resumePrint').disabled = false;
            updateStatus('Print paused', 'info');
        }

        function resumePrint() {
            app.printer.state = 'printing';
            document.getElementById('printerState').textContent = 'Printing';
            document.getElementById('pausePrint').disabled = false;
            document.getElementById('resumePrint').disabled = true;
            updateStatus('Print resumed', 'success');
        }

        function stopPrint() {
            app.printer.state = 'idle';
            document.getElementById('printerState').textContent = 'Idle';
            document.getElementById('pausePrint').disabled = true;
            document.getElementById('resumePrint').disabled = true;
            document.getElementById('stopPrint').disabled = true;
            document.getElementById('startPrint').disabled = false;

            // Reset progress
            app.printer.currentPrint.progress = 0;
            document.getElementById('printProgressBar').style.width = '0%';
            document.getElementById('progressPercentage').textContent = '0%';

            updateStatus('Print stopped', 'info');
        }

        function simulatePrintProgress() {
            const interval = setInterval(() => {
                if (app.printer.state === 'printing') {
                    app.printer.currentPrint.progress += 1;
                    const progress = Math.min(app.printer.currentPrint.progress, 100);

                    document.getElementById('printProgressBar').style.width = progress + '%';
                    document.getElementById('progressPercentage').textContent = progress + '%';
                    document.getElementById('printProgress').textContent = progress + '%';

                    // Update time
                    app.printer.currentPrint.timeElapsed += 60; // 1 minute per update
                    const elapsed = formatTime(app.printer.currentPrint.timeElapsed);
                    document.getElementById('timeElapsed').textContent = elapsed;

                    if (progress >= 100) {
                        clearInterval(interval);
                        stopPrint();
                        updateStatus('Print completed!', 'success');
                    }
                } else if (app.printer.state === 'stopped' || app.printer.state === 'idle') {
                    clearInterval(interval);
                }
            }, 1000); // Update every second for demo
        }

        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        function takeSnapshot() {
            updateStatus('Snapshot taken', 'success');
            // In a real implementation, this would capture from the camera feed
        }

        function toggleCameraFullscreen() {
            const cameraFeed = document.getElementById('cameraFeed');
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                cameraFeed.requestFullscreen();
            }
        }

        // Slicer Functions
        function loadSTLFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.stl,.obj';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        loadModelFromData(event.target.result, file.name);
                    };
                    reader.readAsArrayBuffer(file);
                }
            };
            input.click();
        }

        function loadModelFromData(data, filename) {
            try {
                updateStatus('Loading model...', 'loading');

                const loader = new STLLoader();
                const geometry = loader.parse(data);

                // Create material with random color for distinction
                const colors = [0x4A90E2, 0xFF6B35, 0x7ED321, 0xE74C3C, 0x9B59B6, 0xF39C12];
                const color = colors[app.slicer.loadedModels.length % colors.length];
                const material = new THREE.MeshPhongMaterial({
                    color: color,
                    shininess: 100
                });

                // Create mesh
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                mesh.userData = { filename: filename, id: Date.now() };

                // Center the model
                const box = new THREE.Box3().setFromObject(mesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                mesh.position.sub(center);
                mesh.position.y = size.y / 2;

                // Add to scene and object list
                app.slicer.scene.add(mesh);
                app.slicer.loadedModels.push(mesh);

                console.log('Model added to slicer scene:', {
                    filename: filename,
                    triangles: geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3,
                    size: size,
                    position: mesh.position
                });

                // Update object list UI
                updateObjectList();

                // Fit view
                fitSlicerView();

                updateStatus('Model loaded successfully', 'success');

            } catch (error) {
                console.error('Failed to load model:', error);
                updateStatus('Failed to load model', 'error');
            }
        }

        function clearAllObjects() {
            // Remove all models from scene
            app.slicer.loadedModels.forEach(model => {
                app.slicer.scene.remove(model);
                model.geometry.dispose();
                model.material.dispose();
            });

            // Clear the array
            app.slicer.loadedModels = [];

            // Update UI
            updateObjectList();

            // Reset layer controls
            document.getElementById('currentLayer').textContent = '0 / 0';
            document.getElementById('layerSlider').max = 0;
            document.getElementById('layerSlider').value = 0;
            document.getElementById('layerHeight').textContent = '0.00mm';

            updateStatus('All objects cleared', 'success');
        }

        function updateObjectList() {
            const objectList = document.getElementById('objectList');

            if (app.slicer.loadedModels.length === 0) {
                objectList.innerHTML = '<div class="text-xs text-gray-400 text-center py-2">No objects loaded</div>';
                return;
            }

            objectList.innerHTML = app.slicer.loadedModels.map((model, index) => `
                <div class="flex items-center justify-between bg-bambu-light-gray rounded p-2">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded" style="background-color: #${model.material.color.getHexString()}"></div>
                        <span class="text-xs text-white truncate">${model.userData.filename}</span>
                    </div>
                    <button onclick="removeObject(${index})" class="text-red-400 hover:text-red-300 text-xs">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        function removeObject(index) {
            if (index >= 0 && index < app.slicer.loadedModels.length) {
                const model = app.slicer.loadedModels[index];
                app.slicer.scene.remove(model);
                model.geometry.dispose();
                model.material.dispose();

                app.slicer.loadedModels.splice(index, 1);
                updateObjectList();

                updateStatus('Object removed', 'success');
            }
        }

        // Make removeObject globally accessible
        window.removeObject = removeObject;

        function sendToSlicer() {
            if (!app.generator.currentMesh) {
                updateStatus('No model to send', 'error');
                return;
            }

            try {
                // Switch to slicer tab
                switchTab('slicer');

                // Clone the geometry
                const geometry = app.generator.currentMesh.geometry.clone();

                // Create material with color based on existing objects
                const colors = [0x4A90E2, 0xFF6B35, 0x7ED321, 0xE74C3C, 0x9B59B6, 0xF39C12];
                const color = colors[app.slicer.loadedModels.length % colors.length];
                const material = new THREE.MeshPhongMaterial({
                    color: color,
                    shininess: 100
                });

                // Create mesh
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                mesh.userData = { filename: 'Generated Container', id: Date.now() };

                // Center the model
                const box = new THREE.Box3().setFromObject(mesh);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                mesh.position.sub(center);
                mesh.position.y = size.y / 2;

                // Add to scene and object list
                app.slicer.scene.add(mesh);
                app.slicer.loadedModels.push(mesh);

                console.log('Model added to slicer scene:', {
                    filename: 'Generated Container',
                    triangles: geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3,
                    size: size,
                    position: mesh.position
                });

                // Update object list UI
                updateObjectList();

                // Fit view
                fitSlicerView();

                updateStatus('Model sent to slicer', 'success');

            } catch (error) {
                console.error('Failed to send to slicer:', error);
                updateStatus('Failed to send to slicer', 'error');
            }
        }

        function sliceModel() {
            if (!app.slicer.loadedModel) {
                updateStatus('No model loaded', 'error');
                return;
            }

            updateStatus('Slicing model...', 'loading');

            // Show progress bar
            document.getElementById('sliceProgress').classList.remove('hidden');

            // Simulate slicing process
            simulateSlicing();
        }

        function simulateSlicing() {
            const steps = [
                { progress: 10, text: 'Analyzing geometry...' },
                { progress: 25, text: 'Generating layers...' },
                { progress: 50, text: 'Calculating infill...' },
                { progress: 75, text: 'Adding supports...' },
                { progress: 90, text: 'Optimizing paths...' },
                { progress: 100, text: 'Slicing complete!' }
            ];

            let currentStep = 0;

            const updateProgress = () => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    document.getElementById('progressBar').style.width = step.progress + '%';
                    document.getElementById('progressText').textContent = step.text;
                    currentStep++;
                    setTimeout(updateProgress, 500);
                } else {
                    // Slicing complete
                    setTimeout(() => {
                        document.getElementById('sliceProgress').classList.add('hidden');
                        calculatePrintEstimation();
                        generateLayers();
                        document.getElementById('exportGcode').disabled = false;
                        updateStatus('Model sliced successfully', 'success');
                    }, 500);
                }
            };

            updateProgress();
        }

        function calculatePrintEstimation() {
            if (!app.slicer.loadedModel) return;

            const geometry = app.slicer.loadedModel.geometry;
            const box = new THREE.Box3().setFromObject(app.slicer.loadedModel);
            const size = box.getSize(new THREE.Vector3());

            // Calculate volume (simplified)
            const volume = size.x * size.y * size.z; // mm³
            const layerHeight = app.slicer.sliceSettings.layerHeight;
            const infillDensity = app.slicer.sliceSettings.infillDensity / 100;

            // Estimate material usage
            const shellVolume = volume * 0.3; // Approximate shell volume
            const infillVolume = volume * 0.7 * infillDensity; // Infill volume
            const totalVolume = shellVolume + infillVolume; // mm³

            // Convert to grams
            const materialType = app.slicer.sliceSettings.materialType;
            const density = materialDensities[materialType]; // g/cm³
            const materialGrams = (totalVolume / 1000) * density; // Convert mm³ to cm³, then to grams

            // Estimate print time (simplified)
            const layers = Math.ceil(size.y / layerHeight);
            const printSpeed = app.slicer.sliceSettings.printSpeed; // mm/s
            const estimatedTime = (layers * 100) / printSpeed; // Very simplified estimation
            const hours = Math.floor(estimatedTime / 3600);
            const minutes = Math.floor((estimatedTime % 3600) / 60);

            // Estimate cost (assuming $25/kg for PLA)
            const costPerGram = 0.025; // $0.025 per gram
            const estimatedCost = materialGrams * costPerGram;

            // Update UI
            document.getElementById('printTime').textContent = `${hours}h ${minutes}m`;
            document.getElementById('materialUsage').textContent = `${materialGrams.toFixed(1)} g`;
            document.getElementById('totalLayers').textContent = layers.toString();
            document.getElementById('printCost').textContent = `$${estimatedCost.toFixed(2)}`;

            app.slicer.totalLayers = layers;
        }

        function generateLayers() {
            // Simulate layer generation
            const totalLayers = app.slicer.totalLayers || 100;
            app.slicer.layers = [];

            for (let i = 0; i < totalLayers; i++) {
                app.slicer.layers.push({
                    number: i + 1,
                    height: i * app.slicer.sliceSettings.layerHeight,
                    // In a real slicer, this would contain actual layer geometry
                });
            }

            // Update layer controls
            document.getElementById('layerSlider').max = totalLayers;
            document.getElementById('currentLayer').textContent = `1 / ${totalLayers}`;
            app.slicer.currentLayer = 1;
        }

        function updateInfillDisplay() {
            const value = document.getElementById('infillDensity').value;
            document.getElementById('infillValue').textContent = value + '%';
            app.slicer.sliceSettings.infillDensity = parseInt(value);
        }

        function updateMaterialSettings() {
            const materialType = document.getElementById('materialType').value;
            app.slicer.sliceSettings.materialType = materialType;

            // Update default temperatures based on material
            const tempSettings = {
                pla: { nozzle: 210, bed: 60 },
                abs: { nozzle: 250, bed: 80 },
                petg: { nozzle: 230, bed: 70 },
                tpu: { nozzle: 220, bed: 50 }
            };

            const temps = tempSettings[materialType];
            document.getElementById('nozzleTemp').value = temps.nozzle;
            document.getElementById('bedTemp').value = temps.bed;
            app.slicer.sliceSettings.nozzleTemp = temps.nozzle;
            app.slicer.sliceSettings.bedTemp = temps.bed;
        }

        function updateSliceSettings() {
            app.slicer.sliceSettings.layerHeight = parseFloat(document.getElementById('layerHeight').value);
            app.slicer.sliceSettings.printSpeed = parseInt(document.getElementById('printSpeed').value);
            app.slicer.sliceSettings.nozzleTemp = parseInt(document.getElementById('nozzleTemp').value);
            app.slicer.sliceSettings.bedTemp = parseInt(document.getElementById('bedTemp').value);
        }

        function setSupportMode(enabled) {
            app.slicer.sliceSettings.supportStructures = enabled;

            // Update button states
            const noneBtn = document.getElementById('supportNone');
            const autoBtn = document.getElementById('supportAuto');

            if (enabled) {
                noneBtn.className = 'px-3 py-2 bg-bambu-light-gray text-gray-300 rounded-lg text-xs';
                autoBtn.className = 'px-3 py-2 bg-bambu-blue text-white rounded-lg text-xs';
            } else {
                noneBtn.className = 'px-3 py-2 bg-bambu-blue text-white rounded-lg text-xs';
                autoBtn.className = 'px-3 py-2 bg-bambu-light-gray text-gray-300 rounded-lg text-xs';
            }
        }

        function changeLayer(delta) {
            const newLayer = Math.max(1, Math.min(app.slicer.totalLayers || 100, app.slicer.currentLayer + delta));
            app.slicer.currentLayer = newLayer;
            document.getElementById('layerSlider').value = newLayer;
            updateLayerDisplay();
        }

        function updateLayerDisplay() {
            const current = app.slicer.currentLayer;
            const total = app.slicer.totalLayers || 0;
            const layerHeight = app.slicer.sliceSettings.layerHeight;

            document.getElementById('currentLayer').textContent = `${current} / ${total}`;
            document.getElementById('layerHeight').textContent = `${(current * layerHeight).toFixed(2)}mm`;

            // Simulate layer-by-layer visualization
            app.slicer.loadedModels.forEach(model => {
                if (total > 0) {
                    // Create a clipping plane to show layers from bottom up
                    const currentHeight = current * layerHeight;

                    // Get model bounds
                    const box = new THREE.Box3().setFromObject(model);
                    const modelHeight = box.max.y - box.min.y;

                    if (currentHeight >= modelHeight) {
                        // Show full model
                        model.material.opacity = 1.0;
                        model.material.transparent = false;
                    } else {
                        // Show partial model up to current layer
                        const progress = currentHeight / modelHeight;
                        model.material.opacity = 0.3 + progress * 0.7;
                        model.material.transparent = true;

                        // Add a cutting plane effect
                        if (!model.userData.originalColor) {
                            model.userData.originalColor = model.material.color.getHex();
                        }

                        // Highlight current layer with orange tint
                        const layerProgress = (current / total);
                        const orange = new THREE.Color(0xFF6B35);
                        const original = new THREE.Color(model.userData.originalColor);
                        model.material.color = original.clone().lerp(orange, layerProgress * 0.3);
                    }
                } else {
                    // No layers, show full model
                    model.material.opacity = 1.0;
                    model.material.transparent = false;
                    if (model.userData.originalColor) {
                        model.material.color.setHex(model.userData.originalColor);
                    }
                }
            });
        }

        function fitSlicerView() {
            if (app.slicer.loadedModels.length === 0) return;

            // Calculate bounding box for all objects
            const box = new THREE.Box3();
            app.slicer.loadedModels.forEach(model => {
                box.expandByObject(model);
            });

            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            const maxDim = Math.max(size.x, size.y, size.z);
            const distance = maxDim * 2.5; // Slightly more distance for better view

            app.slicer.camera.position.copy(center);
            app.slicer.camera.position.x += distance;
            app.slicer.camera.position.y += distance * 0.5;
            app.slicer.camera.position.z += distance;
            app.slicer.camera.lookAt(center);

            app.slicer.controls.target.copy(center);
            app.slicer.controls.update();
        }

        function exportGcode() {
            if (!app.slicer.loadedModel) {
                updateStatus('No sliced model to export', 'error');
                return;
            }

            updateStatus('Generating G-code...', 'loading');

            // Simulate G-code generation
            setTimeout(() => {
                const gcode = generateSimpleGcode();

                const blob = new Blob([gcode], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'model.gcode';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                updateStatus('G-code exported successfully', 'success');
            }, 1000);
        }

        function generateSimpleGcode() {
            const settings = app.slicer.sliceSettings;

            return `; Generated by Bambu 3D Suite
; Print Settings:
; Layer Height: ${settings.layerHeight}mm
; Infill: ${settings.infillDensity}%
; Print Speed: ${settings.printSpeed}mm/s
; Nozzle Temp: ${settings.nozzleTemp}°C
; Bed Temp: ${settings.bedTemp}°C

; Start G-code
M104 S${settings.nozzleTemp} ; Set nozzle temperature
M140 S${settings.bedTemp} ; Set bed temperature
G28 ; Home all axes
G1 Z15.0 F6000 ; Move Z axis up

; Print layers would be here...
; (This is a simplified example)

; End G-code
M104 S0 ; Turn off nozzle
M140 S0 ; Turn off bed
G28 X0 ; Home X axis
M84 ; Disable steppers
`;
        }

        // Generator Functions (from previous implementation)
        const shapeConfigs = {
            cylinder: {
                name: 'Cylinder',
                icon: 'fas fa-circle',
                params: [
                    { id: 'diameter', label: 'Diameter (mm)', type: 'number', value: 80, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 50, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 2, min: 0.1, max: 50, step: 0.1 },
                    { id: 'segments', label: 'Quality (segments)', type: 'range', value: 32, min: 8, max: 64 }
                ]
            },
            box: {
                name: 'Box',
                icon: 'fas fa-cube',
                params: [
                    { id: 'length', label: 'Length (mm)', type: 'number', value: 100, min: 1, max: 500 },
                    { id: 'width', label: 'Width (mm)', type: 'number', value: 60, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 40, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 3, min: 0.1, max: 50, step: 0.1 }
                ]
            },
            polygon: {
                name: 'Polygon',
                icon: 'fas fa-shapes',
                params: [
                    { id: 'diameter', label: 'Diameter (mm)', type: 'number', value: 80, min: 1, max: 500 },
                    { id: 'height', label: 'Height (mm)', type: 'number', value: 50, min: 1, max: 500 },
                    { id: 'wallThickness', label: 'Wall Thickness (mm)', type: 'number', value: 2, min: 0.1, max: 50, step: 0.1 }
                ]
            }
        };

        function getShapeType(sides) {
            if (sides === 1) return 'cylinder';
            if (sides === 4) return 'box';
            return 'polygon';
        }

        function updateShapeConfig() {
            const shapeType = getShapeType(app.generator.currentSides);
            const config = shapeConfigs[shapeType];

            document.getElementById('shapeType').textContent = config.name;

            const configPanel = document.getElementById('configPanel');
            configPanel.innerHTML = `
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 text-sm font-semibold text-bambu-orange">
                        <i class="${config.icon}"></i>
                        <span>${config.name} Configuration</span>
                    </div>
                    ${config.params.map(param => createParamHTML(param)).join('')}
                </div>
            `;

            config.params.forEach(param => {
                const element = document.getElementById(param.id);
                if (element) {
                    element.addEventListener('input', updateGeometry);
                }
            });
        }

        function createParamHTML(param) {
            const inputClass = param.type === 'range'
                ? 'w-full h-2 bg-bambu-light-gray rounded-lg appearance-none cursor-pointer'
                : 'w-full px-3 py-2 bg-bambu-light-gray border border-gray-600 rounded-lg text-white focus:border-bambu-blue focus:outline-none';

            return `
                <div class="space-y-2">
                    <label class="block text-sm text-gray-300">${param.label}</label>
                    <input type="${param.type}" id="${param.id}"
                           value="${param.value}"
                           min="${param.min || ''}"
                           max="${param.max || ''}"
                           step="${param.step || ''}"
                           class="${inputClass}">
                    ${param.type === 'range' ? `
                        <div class="flex justify-between text-xs text-gray-400">
                            <span>${param.min}</span>
                            <span id="${param.id}Value">${param.value}</span>
                            <span>${param.max}</span>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function getCurrentParams() {
            const shapeType = getShapeType(app.generator.currentSides);
            const config = shapeConfigs[shapeType];
            const params = { sides: app.generator.currentSides };

            config.params.forEach(param => {
                const element = document.getElementById(param.id);
                if (element) {
                    params[param.id] = parseFloat(element.value) || param.value;

                    if (param.type === 'range') {
                        const valueDisplay = document.getElementById(param.id + 'Value');
                        if (valueDisplay) {
                            valueDisplay.textContent = element.value;
                        }
                    }
                }
            });

            return params;
        }

        function updateGeometry() {
            try {
                updateStatus('Generating geometry...', 'loading');

                if (app.generator.currentMesh) {
                    app.generator.scene.remove(app.generator.currentMesh);
                    app.generator.currentMesh.geometry.dispose();
                    app.generator.currentMesh.material.dispose();
                    app.generator.currentMesh = null;
                }

                const params = getCurrentParams();
                let geometry;

                if (app.generator.currentSides === 1) {
                    geometry = createHollowCylinderGeometry(
                        params.diameter / 2,
                        params.height,
                        params.wallThickness,
                        params.segments
                    );
                } else if (app.generator.currentSides === 4) {
                    geometry = createHollowBoxGeometry(
                        params.length,
                        params.width,
                        params.height,
                        params.wallThickness
                    );
                } else {
                    geometry = createHollowCylinderGeometry(
                        params.diameter / 2,
                        params.height,
                        params.wallThickness,
                        app.generator.currentSides
                    );
                }

                const material = new THREE.MeshPhongMaterial({
                    color: 0x4A90E2,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });

                app.generator.currentMesh = new THREE.Mesh(geometry, material);
                app.generator.currentMesh.castShadow = true;
                app.generator.currentMesh.receiveShadow = true;

                app.generator.scene.add(app.generator.currentMesh);

                const triangleCount = geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3;
                document.getElementById('triangleCount').textContent = Math.floor(triangleCount);
                document.getElementById('currentShapeInfo').textContent = getShapeType(app.generator.currentSides);

                // Calculate volume
                const box = new THREE.Box3().setFromObject(app.generator.currentMesh);
                const size = box.getSize(new THREE.Vector3());
                const volume = (size.x * size.y * size.z) / 1000; // Convert to cm³
                document.getElementById('volumeInfo').textContent = volume.toFixed(1) + ' cm³';

                updateStatus('Geometry generated successfully', 'success');

            } catch (error) {
                console.error('Geometry generation failed:', error);
                updateStatus('Generation failed', 'error');
            }
        }

        function setRenderMode(mode) {
            if (!app.generator.currentMesh) return;

            app.generator.currentMesh.material.dispose();

            if (mode === 'wireframe') {
                app.generator.currentMesh.material = new THREE.MeshBasicMaterial({
                    color: 0x4A90E2,
                    wireframe: true,
                    transparent: true,
                    opacity: 1
                });

                document.getElementById('solidMode').className = 'px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600';
                document.getElementById('wireframeMode').className = 'px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600';
            } else {
                app.generator.currentMesh.material = new THREE.MeshPhongMaterial({
                    color: 0x4A90E2,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });

                document.getElementById('solidMode').className = 'px-4 py-2 bg-bambu-blue text-white rounded-lg transition-all hover:bg-blue-600';
                document.getElementById('wireframeMode').className = 'px-4 py-2 bg-bambu-light-gray text-gray-300 rounded-lg transition-all hover:bg-gray-600';
            }
        }

        function resetGeneratorView() {
            if (!app.generator.camera || !app.generator.controls) return;
            app.generator.camera.position.set(150, 100, 150);
            app.generator.camera.lookAt(0, 0, 0);
            app.generator.controls.reset();
            updateStatus('View reset', 'success');
        }

        function fitGeneratorView() {
            if (!app.generator.currentMesh || !app.generator.camera || !app.generator.controls) return;

            const box = new THREE.Box3().setFromObject(app.generator.currentMesh);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            const maxDim = Math.max(size.x, size.y, size.z);
            const distance = maxDim * 2;

            app.generator.camera.position.copy(center);
            app.generator.camera.position.x += distance;
            app.generator.camera.position.y += distance * 0.5;
            app.generator.camera.position.z += distance;
            app.generator.camera.lookAt(center);

            app.generator.controls.target.copy(center);
            app.generator.controls.update();

            updateStatus('View fitted', 'success');
        }

        function exportSTL() {
            if (!app.generator.currentMesh) {
                updateStatus('No geometry to export', 'error');
                return;
            }

            try {
                updateStatus('Exporting STL...', 'loading');

                const geometry = app.generator.currentMesh.geometry;
                const positionAttribute = geometry.attributes.position;
                const indexAttribute = geometry.index;

                let stl = 'solid container\n';
                let triangleCount = 0;

                if (indexAttribute) {
                    for (let i = 0; i < indexAttribute.count; i += 3) {
                        const i1 = indexAttribute.getX(i);
                        const i2 = indexAttribute.getX(i + 1);
                        const i3 = indexAttribute.getX(i + 2);

                        const v1 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i1);
                        const v2 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i2);
                        const v3 = new THREE.Vector3().fromBufferAttribute(positionAttribute, i3);

                        const normal = new THREE.Vector3();
                        const edge1 = new THREE.Vector3().subVectors(v2, v1);
                        const edge2 = new THREE.Vector3().subVectors(v3, v1);
                        normal.crossVectors(edge1, edge2).normalize();

                        if (normal.length() > 0.001) {
                            stl += `  facet normal ${normal.x.toFixed(6)} ${normal.y.toFixed(6)} ${normal.z.toFixed(6)}\n`;
                            stl += `    outer loop\n`;
                            stl += `      vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                            stl += `      vertex ${v2.x.toFixed(6)} ${v2.y.toFixed(6)} ${v2.z.toFixed(6)}\n`;
                            stl += `      vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                            stl += `    endloop\n`;
                            stl += `  endfacet\n`;
                            triangleCount++;
                        }
                    }
                }

                stl += 'endsolid container\n';

                const blob = new Blob([stl], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${getShapeType(app.generator.currentSides)}_${app.generator.currentSides}sides_container.stl`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                updateStatus(`STL exported - ${triangleCount} triangles`, 'success');

            } catch (error) {
                console.error('STL export failed:', error);
                updateStatus('Export failed', 'error');
            }
        }

        // Geometry generation functions (reusing from previous implementation)
        function createHollowBoxGeometry(length, width, height, wallThickness) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            const t = Math.min(wallThickness, Math.min(length, width, height) / 4);
            const l = length, w = width, h = height;
            const il = Math.max(0, l - 2 * t);
            const iw = Math.max(0, w - 2 * t);
            const ih = Math.max(0, h - t);

            // Outer vertices
            vertices.push(
                -l/2, 0, -w/2, l/2, 0, -w/2, l/2, 0, w/2, -l/2, 0, w/2,
                -l/2, h, -w/2, l/2, h, -w/2, l/2, h, w/2, -l/2, h, w/2
            );

            if (il > 0 && iw > 0 && ih > 0) {
                vertices.push(
                    -il/2, t, -iw/2, il/2, t, -iw/2, il/2, t, iw/2, -il/2, t, iw/2,
                    -il/2, h, -iw/2, il/2, h, -iw/2, il/2, h, iw/2, -il/2, h, iw/2
                );
            }

            // Create faces
            indices.push(0, 1, 2, 0, 2, 3); // Bottom
            indices.push(0, 4, 5, 0, 5, 1, 1, 5, 6, 1, 6, 2, 2, 6, 7, 2, 7, 3, 3, 7, 4, 3, 4, 0); // Sides

            if (il > 0 && iw > 0 && ih > 0) {
                indices.push(8, 10, 9, 8, 11, 10); // Inner bottom
                indices.push(8, 9, 13, 8, 13, 12, 9, 10, 14, 9, 14, 13, 10, 11, 15, 10, 15, 14, 11, 8, 12, 11, 12, 15); // Inner sides
                indices.push(0, 8, 9, 0, 9, 1, 1, 9, 10, 1, 10, 2, 2, 10, 11, 2, 11, 3, 3, 11, 8, 3, 8, 0); // Bottom walls
                indices.push(4, 12, 13, 4, 13, 5, 5, 13, 14, 5, 14, 6, 6, 14, 15, 6, 15, 7, 7, 15, 12, 7, 12, 4); // Top rim
            } else {
                indices.push(4, 6, 5, 4, 7, 6); // Solid top
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();
            return geometry;
        }

        function createHollowCylinderGeometry(radius, height, wallThickness, segments) {
            const geometry = new THREE.BufferGeometry();
            const vertices = [];
            const indices = [];

            const t = Math.min(wallThickness, radius / 2);
            const innerRadius = Math.max(0, radius - t);
            const innerHeight = Math.max(0, height - t);

            // Outer cylinder
            vertices.push(0, 0, 0); // Center bottom
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(Math.cos(angle) * radius, 0, Math.sin(angle) * radius);
            }

            vertices.push(0, height, 0); // Center top
            for (let i = 0; i < segments; i++) {
                const angle = (i / segments) * Math.PI * 2;
                vertices.push(Math.cos(angle) * radius, height, Math.sin(angle) * radius);
            }

            // Inner cylinder
            if (innerRadius > 0 && innerHeight > 0) {
                vertices.push(0, t, 0); // Inner center bottom
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(Math.cos(angle) * innerRadius, t, Math.sin(angle) * innerRadius);
                }

                vertices.push(0, height, 0); // Inner center top
                for (let i = 0; i < segments; i++) {
                    const angle = (i / segments) * Math.PI * 2;
                    vertices.push(Math.cos(angle) * innerRadius, height, Math.sin(angle) * innerRadius);
                }
            }

            // Create faces
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                indices.push(0, i + 1, next + 1); // Outer bottom
            }

            const outerTopStart = segments + 2;
            for (let i = 0; i < segments; i++) {
                const next = (i + 1) % segments;
                const bottom1 = i + 1, bottom2 = next + 1;
                const top1 = outerTopStart + i, top2 = outerTopStart + next;
                indices.push(bottom1, top1, top2, bottom1, top2, bottom2); // Outer sides
            }

            if (innerRadius > 0 && innerHeight > 0) {
                const innerBottomStart = segments * 2 + 3;
                const innerTopStart = segments * 3 + 4;

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(innerBottomStart, innerBottomStart + next, innerBottomStart + i); // Inner bottom
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const bottom1 = innerBottomStart + i, bottom2 = innerBottomStart + next;
                    const top1 = innerTopStart + i, top2 = innerTopStart + next;
                    indices.push(bottom1, top2, top1, bottom1, bottom2, top2); // Inner sides
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = i + 1, outer2 = next + 1;
                    const inner1 = innerBottomStart + i, inner2 = innerBottomStart + next;
                    indices.push(outer1, inner1, inner2, outer1, inner2, outer2); // Bottom wall
                }

                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    const outer1 = outerTopStart + i, outer2 = outerTopStart + next;
                    const inner1 = innerTopStart + i, inner2 = innerTopStart + next;
                    indices.push(outer1, inner2, inner1, outer1, outer2, inner2); // Top rim
                }
            } else {
                for (let i = 0; i < segments; i++) {
                    const next = (i + 1) % segments;
                    indices.push(segments + 1, outerTopStart + next, outerTopStart + i); // Solid top
                }
            }

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();
            return geometry;
        }

        // Initialize when page loads
        window.addEventListener('load', init);
        
    </script>
</body>
</html>
